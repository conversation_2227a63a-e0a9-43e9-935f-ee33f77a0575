# Notes Feature - PenguinManager

## Overview

The Notes feature provides a Workflowy-style hierarchical note-taking system with inline editing, keyboard navigation, and a card-based grid overview.

## Features

### Grid View (`/notes`)
- **Card-based layout**: Notes are displayed as cards with titles and previews
- **Search functionality**: Real-time search through all notes and their content
- **Quick actions**: Delete, duplicate, and tag notes (tag functionality placeholder)
- **Responsive design**: Adapts to different screen sizes
- **Visual indicators**: Search results are highlighted

### Detail View (`/notes/:id`)
- **Workflowy-style editor**: Hierarchical bullet list with inline editing
- **Keyboard navigation**:
  - `Enter`: Create new bullet below current
  - `Tab`: Indent (make child of previous sibling)
  - `Shift+Tab`: Outdent (move to parent's level)
  - `Backspace`: Delete empty bullet
  - `Arrow Up/Down`: Navigate between bullets
- **Breadcrumb navigation**: Shows path to current note
- **Focus management**: Automatic focus on new bullets with cursor positioning

### State Management
- **NotesProvider**: Context-based state management at route level
- **Persistent state**: Notes state is shared between grid and detail views
- **Tree structure**: Supports unlimited nesting levels

## Technical Implementation

### Components
- `NotesPage`: Grid view with search and card layout
- `NoteDetailPage`: Workflowy-style editor
- `SearchBar`: Real-time search input
- `BreadcrumbNav`: Navigation breadcrumbs
- `ListItem`: Individual note card component

### Utilities
- `treeHelpers.ts`: Tree navigation and search utilities
- `useAutoSave.ts`: Debounced auto-save hook
- `useKeyboardNavigation.ts`: Enhanced keyboard navigation

### Data Structure
```typescript
interface Note {
  id: string;
  content: string;
  parentId: string | null;
  children: string[];
  createdAt: string;
  updatedAt: string;
}
```

## Usage

### Creating Notes
1. Click "+ New Note" button in grid view
2. Click on any note card to open detail view
3. Use `Enter` to create new bullets

### Editing Notes
1. Click on any bullet in detail view
2. Type to edit content
3. Use keyboard shortcuts for navigation and structure

### Searching
1. Use the search bar in grid view
2. Search matches note titles and content
3. Results are highlighted in the grid

### Navigation
1. Use breadcrumbs to navigate to parent notes
2. Use "Back to Notes" button to return to grid
3. Click on note cards to open detail view

## Keyboard Shortcuts

### In Detail View
- `Enter`: Create new bullet below
- `Tab`: Indent current bullet
- `Shift+Tab`: Outdent current bullet
- `Backspace`: Delete empty bullet
- `Arrow Up`: Move to previous bullet
- `Arrow Down`: Move to next bullet

## Sample Data

The app includes sample notes to demonstrate the functionality:
- Welcome to PenguinManager Notes
  - Getting Started
    - Create your first note by clicking the + button
  - Features
- Project Ideas
  - Web Application
  - Mobile App

## Future Enhancements

- [ ] Tag system implementation
- [ ] Export/import functionality
- [ ] Collaborative editing
- [ ] Rich text formatting
- [ ] File attachments
- [ ] Version history
- [ ] Offline support
- [ ] Advanced search filters 