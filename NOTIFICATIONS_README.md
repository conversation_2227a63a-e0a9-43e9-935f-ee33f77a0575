# Notifications & Reminders Feature

## Overview

The task manager now includes a comprehensive notifications and reminders system that helps users stay on top of their tasks with browser notifications, sound alerts, and customizable reminder settings.

## Features Implemented

### 1. **Reminder Management**
- **Due Date Reminders**: Automatically remind users about tasks due soon
- **Custom Reminders**: Set custom reminder times for any task
- **Reminder Messages**: Add custom messages to reminders
- **Active/Inactive Toggle**: Enable or disable individual reminders
- **Reminder Deletion**: Remove unwanted reminders

### 2. **Browser Notifications**
- **Desktop Notifications**: Show notifications on the user's desktop
- **Sound Notifications**: Play audio alerts when notifications appear
- **Permission Management**: Handle browser notification permissions
- **Notification Actions**: Click notifications to focus the app

### 3. **Settings & Configuration**
- **Notification Settings Page**: Comprehensive settings management
- **Permission Status**: Display current notification permission status
- **Granular Controls**: Toggle individual notification features
- **User-Friendly UI**: Modern toggle switches and clear descriptions

### 4. **Integration Points**
- **Task Creation**: Add reminders when creating new tasks
- **Task Details**: Manage reminders in the task detail modal
- **Background Checking**: Automatic reminder checking every minute
- **State Management**: Integrated with existing app state

## Technical Implementation

### Components Created

1. **NotificationManager** (`src/components/NotificationManager.tsx`)
   - Background service for checking reminders
   - Browser notification handling
   - Sound notification support
   - Automatic reminder deactivation

2. **ReminderForm** (`src/components/ReminderForm.tsx`)
   - Date/time picker for reminder scheduling
   - Reminder type selection (due date vs custom)
   - Custom message input
   - Form validation

3. **Updated Components**
   - **App.tsx**: Added notification state and reminder management
   - **TaskForm.tsx**: Integrated reminder creation
   - **TaskDetailModal.tsx**: Added reminder management UI
   - **Settings.tsx**: Comprehensive notification settings

### Data Types

```typescript
export type Reminder = {
  id: string;
  taskId: string;
  type: 'due_date' | 'custom';
  time: string; // ISO string
  message: string;
  isActive: boolean;
  createdAt: string;
};

export type Task = {
  // ... existing fields
  reminders?: Reminder[];
};
```

### State Management

The notification system uses React state management with:
- Global notification settings in App.tsx
- Task-specific reminders stored with each task
- Real-time permission checking
- Automatic state synchronization

## User Experience

### Adding Reminders
1. **During Task Creation**: Click the "Reminders" button in the task form
2. **In Task Details**: Use the "Add Reminder" button in the task detail modal
3. **Reminder Types**:
   - **Due Date**: Automatically set based on task due date
   - **Custom**: Set any specific date and time

### Managing Reminders
- **View**: See all reminders in task detail modal
- **Toggle**: Enable/disable reminders with checkboxes
- **Delete**: Remove reminders with the trash icon
- **Edit**: Recreate reminders as needed

### Notification Settings
Access via the Settings page:
- **Enable/Disable**: Master notification toggle
- **Desktop Notifications**: Show browser notifications
- **Sound**: Play audio alerts
- **Due Date Reminders**: Automatic due date notifications
- **Custom Reminders**: Allow custom reminder times

## Browser Compatibility

### Supported Features
- ✅ Browser Notifications API
- ✅ Audio API for sound notifications
- ✅ Modern React hooks and state management
- ✅ Responsive design with Tailwind CSS

### Browser Requirements
- Modern browsers with Notification API support
- HTTPS required for notifications in production
- User permission required for desktop notifications

## Future Enhancements

### Planned Features
1. **Email Notifications**: Send email reminders
2. **Push Notifications**: Mobile push notifications
3. **Recurring Reminders**: Set up recurring reminder schedules
4. **Notification History**: Track sent notifications
5. **Advanced Scheduling**: More granular time options
6. **Notification Templates**: Predefined reminder messages

### Technical Improvements
1. **Service Worker**: Background notification handling
2. **Offline Support**: Queue notifications when offline
3. **Notification Actions**: Quick actions from notifications
4. **Sound Customization**: Multiple notification sounds
5. **Notification Groups**: Group related notifications

## Usage Examples

### Setting a Due Date Reminder
1. Create a task with a due date
2. Click "Reminders" in the task form
3. Select "Due Date" type
4. Choose when to be reminded (e.g., 1 day before)
5. Add optional custom message
6. Save the reminder

### Setting a Custom Reminder
1. Open any task detail modal
2. Click "Add Reminder"
3. Select "Custom Time"
4. Pick specific date and time
5. Add custom message
6. Save the reminder

### Managing Notification Settings
1. Go to Settings page
2. Request notification permission if needed
3. Enable notifications
4. Configure desktop and sound settings
5. Set reminder preferences

## Troubleshooting

### Common Issues
1. **Notifications not showing**: Check browser permission settings
2. **Sound not playing**: Ensure sound is enabled in settings
3. **Reminders not triggering**: Verify notification settings are enabled
4. **Permission denied**: Guide user to browser settings

### Debug Information
- Check browser console for notification errors
- Verify notification permission status
- Ensure app is running in supported browser
- Check if notifications are enabled in system settings

## Security & Privacy

### Data Handling
- Reminders stored locally in app state
- No external notification services used
- Browser notifications only (no server communication)
- User controls all notification preferences

### Permission Management
- Explicit user consent required
- Clear permission status display
- Easy permission request flow
- Respect user privacy choices

---

This notifications system provides a robust foundation for keeping users informed about their tasks while maintaining a clean, modern user interface and respecting user privacy preferences. 