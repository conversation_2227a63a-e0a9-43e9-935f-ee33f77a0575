# Task Manager App

A modern React 19 + TypeScript task manager application with project management capabilities, built with Tailwind CSS and react-icons.

## Features

### Core Functionality
- **Projects & Tasks**: Full CRUD operations for both projects and tasks
- **Task Assignment**: Tasks can be assigned to projects and reassigned via detail modal
- **Project Favoriting**: Mark projects as favorites for quick access
- **Sidebar Navigation**: Direct project links with URL routing (`/projects/:id`)

### Due Date Management
- **Due Date Filtering**: Filter tasks by due date categories:
  - Today
  - Tomorrow
  - This Week
  - Overdue
  - No Due Date
  - All Tasks
- **Smart Due Date Display**: 
  - Shows "Today", "Tomorrow", "In X days", or "X days ago" for overdue tasks
  - Color-coded indicators (red for overdue, orange for today, yellow for tomorrow)
  - Visual warning icons for overdue tasks

### Task Sorting
- **Multiple Sort Options**:
  - Due Date (ascending/descending)
  - Priority (Urgent > High > Normal > Low)
  - Name (alphabetical)
  - Creation Date
  - Project
- **Sort Direction Toggle**: Switch between ascending and descending order

### Enhanced UI/UX
- **Modern Design**: Clean, accessible interface using Tailwind CSS
- **Responsive Layout**: Works on desktop and mobile devices
- **Visual Priority Indicators**: Color-coded priority badges
- **Project Color Coding**: Each project has a unique color for easy identification
- **Task Status**: Visual completion indicators with checkboxes

## Pages

### Inbox (`/inbox`)
- Default landing page
- Shows tasks not assigned to any project
- Filter by project or due date
- Sort by various criteria

### Today (`/today`)
- Shows tasks due today
- Automatic filtering with additional sorting options

### Upcoming (`/upcoming`)
- Shows tasks due in the next 7 days
- Helps with weekly planning

### Projects (`/projects`)
- Grid/list view of all projects
- Search and filter by favorites
- Direct navigation to project detail view

### Project Detail (`/projects/:id`)
- Shows all tasks for a specific project
- Add new tasks directly to the project
- Filter and sort project tasks

## Technical Stack

- **React 19**: Latest React with hooks and modern patterns
- **TypeScript**: Full type safety throughout the application
- **Tailwind CSS**: Utility-first CSS framework for styling
- **React Icons**: Comprehensive icon library
- **React Router**: Client-side routing with URL synchronization

## State Management

- **Local State**: Uses React hooks for state management
- **Props Drilling**: State is managed at the top level (App.tsx) and passed down
- **URL Sync**: Selected projects are synchronized with URL parameters

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm start
   ```

3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Recent Updates

### Due Date Filtering & Sorting (Latest)
- Added comprehensive due date filtering system
- Implemented multiple sorting options with direction toggle
- Enhanced TaskCard component with smart due date display
- Updated Today and Upcoming pages with filtering capabilities
- Added visual indicators for overdue tasks and priorities

### Previous Fixes
- Direct navigation to `/projects/:id` now correctly selects and displays the project
- Projects page uses `useParams` to sync selected project with URL
- Improved task assignment and project management workflow
