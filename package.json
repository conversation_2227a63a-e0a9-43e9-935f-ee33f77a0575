{"name": "task-manager", "version": "0.1.0", "private": true, "dependencies": {"@chakra-ui/react": "^3.21.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^9.0.7", "autoprefixer": "^10.4.21", "framer-motion": "^12.19.2", "pdfjs-dist": "^5.3.31", "postcss": "^8.4.31", "react": "^19.1.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-force-graph": "^1.47.7", "react-force-graph-2d": "^1.27.1", "react-icons": "^4.12.0", "react-pdf": "^10.0.1", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1", "tailwindcss": "^3.4.17", "typescript": "^4.9.5", "uuid": "^9.0.1", "web-vitals": "^2.1.4"}, "overrides": {"nth-check": "^2.1.1", "svgo": "^3.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}