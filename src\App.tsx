import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Inbox from './pages/Inbox';
import Today from './pages/Today';
import Upcoming from './pages/Upcoming';
import Projects from './pages/Projects';
import Objects from './pages/Objects';
import Settings from './pages/Settings';
import TaskDetails from './pages/TaskDetails';
import Login from './pages/Login';
import TaskForm from './components/TaskForm';
// Placeholder import for the modal
import TaskDetailModal from './components/TaskDetailModal';
import NotificationManager from './components/NotificationManager';
import MentionsTest from './components/MentionsTest';
import Users from './pages/Users';
import Documents from './pages/Documents';
import ChatInterface from './components/chat/ChatInterface';
import NotesPage from './features/notes/pages/NotesPage';
import NoteDetailPage from './features/notes/pages/NoteDetailPage';
import { NotesProvider } from './features/notes/context/NotesContext';

const DRAWER_WIDTH = 240;
const APPBAR_HEIGHT = 64;

export type User = {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  isOnline: boolean;
  lastSeen: string;
};

export type TeamMember = {
  userId: string;
  projectId: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  joinedAt: string;
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canAssign: boolean;
    canComment: boolean;
    canShare: boolean;
  };
};

export type TaskAssignment = {
  id: string;
  taskId: string;
  userId: string;
  assignedBy: string;
  assignedAt: string;
  status: 'pending' | 'accepted' | 'declined';
};

export type Subtask = {
  id: string;
  name: string;
  completed: boolean;
  assignedTo?: string;
};

export type Comment = {
  id: string;
  text: string;
  author: string;
  authorId: string;
  timestamp: string;
  mentions?: string[]; // Array of user IDs mentioned
  replies?: Comment[];
  isEdited: boolean;
  editedAt?: string;
};

export type Reminder = {
  id: string;
  taskId: string;
  type: 'due_date' | 'custom';
  time: string; // ISO string
  message: string;
  isActive: boolean;
  createdAt: string;
};

export type Task = {
  id: string;
  name: string;
  description: string;
  dueToday: boolean;
  dueDate?: string;
  priority?: string;
  tags?: string[];
  comments?: Comment[];
  subtasks?: Subtask[];
  completed: boolean;
  // Kanban workflow support
  status?: 'todo' | 'in-progress' | 'review' | 'done' | 'backlog';
  projectId?: string;
  reminders?: Reminder[];
  // Collaboration features
  assignedTo?: string[];
  createdBy: string;
  createdAt: string;
  updatedBy?: string;
  updatedAt: string;
  isShared: boolean;
  sharedWith?: string[]; // Array of user IDs
  visibility: 'private' | 'team' | 'public';
};

export type Project = {
  id: string;
  name: string;
  description: string;
  color: string;
  isFavorite: boolean;
  createdAt: string;
  updatedAt: string;
  taskCount: number;
  completedTaskCount: number;
  // Kanban board configuration
  kanbanConfig?: {
    enabled: boolean;
    columns: {
      id: string;
      name: string;
      color: string;
      order: number;
      status: string;
    }[];
  };
  // Collaboration features
  createdBy: string;
  teamMembers?: TeamMember[];
  isShared: boolean;
  visibility: 'private' | 'team' | 'public';
  settings: {
    allowComments: boolean;
    allowTaskAssignment: boolean;
    allowExternalSharing: boolean;
    requireApproval: boolean;
  };
};

export type Document = {
  id: string;
  name: string;
  originalName: string;
  type: 'pdf' | 'image' | 'spreadsheet' | 'document' | 'presentation' | 'code' | 'archive' | 'other';
  mimeType: string;
  size: number;
  url?: string;
  thumbnailUrl?: string;
  tags: string[];
  projectId?: string;
  taskId?: string;
  uploadedBy: string;
  uploadedAt: string;
  lastModified: string;
  isShared: boolean;
  sharedWith?: string[];
  visibility: 'private' | 'team' | 'public';
  comments?: Comment[];
  version: number;
  description?: string;
  // Advanced document processing features
  ocrText?: string; // Extracted text from OCR
  contentText?: string; // Full text content for searchable documents
  previewData?: {
    type: 'text' | 'image' | 'pdf' | 'code' | 'spreadsheet';
    content?: string; // Text content for preview
    imageUrl?: string; // Image URL for image previews
    codeLanguage?: string; // Programming language for code files
    spreadsheetData?: {
      sheets: Array<{
        name: string;
        data: string[][]; // 2D array of cell data
      }>;
    };
  };
  processingStatus?: 'pending' | 'processing' | 'completed' | 'failed';
  processingError?: string;
};

const App: React.FC = () => {
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([
    {
      id: '1',
      name: 'Design homepage mockup',
      description: 'Create wireframes for the new homepage design',
      dueToday: false,
      dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 5 days from now
      priority: 'High',
      tags: ['design', 'wireframes'],
      comments: [
        {
          id: 'comment1',
          text: 'Hey @Jane Smith, can you review this design? Also @Mike Johnson, please check the wireframes.',
          author: 'John Doe',
          authorId: 'user1',
          timestamp: new Date().toISOString(),
          mentions: ['user2', 'user3'],
          replies: [],
          isEdited: false
        },
        {
          id: 'comment2',
          text: 'Testing @Sarah Wilson mentions dropdown styling',
          author: 'John Doe',
          authorId: 'user1',
          timestamp: new Date().toISOString(),
          mentions: ['user4'],
          replies: [],
          isEdited: false
        }
      ],
      subtasks: [],
      completed: false,
      projectId: '1', // Website Redesign
      reminders: [
        {
          id: '1',
          taskId: '1',
          type: 'due_date',
          time: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(), // 1 day before due
          message: 'Design homepage mockup is due tomorrow',
          isActive: true,
          createdAt: new Date().toISOString()
        }
      ],
      assignedTo: [],
      createdBy: 'user1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isShared: false,
      visibility: 'private'
    },
    {
      id: '2',
      name: 'Set up development environment',
      description: 'Install and configure React Native development tools',
      dueToday: false,
      dueDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 2 days ago (overdue)
      priority: 'Normal',
      tags: ['setup', 'development'],
      comments: [],
      subtasks: [],
      completed: true,
      projectId: '2', // Mobile App Development
      reminders: [],
      assignedTo: [],
      createdBy: 'user2',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isShared: false,
      visibility: 'private'
    },
    {
      id: '3',
      name: 'Create social media content',
      description: 'Design posts for Instagram and Facebook campaigns',
      dueToday: false,
      dueDate: new Date().toISOString().split('T')[0], // Today
      priority: 'Urgent',
      tags: ['marketing', 'social-media'],
      comments: [],
      subtasks: [],
      completed: false,
      projectId: '3', // Marketing Campaign
      reminders: [
        {
          id: '2',
          taskId: '3',
          type: 'due_date',
          time: new Date().toISOString(), // Due today
          message: 'Create social media content is due today',
          isActive: true,
          createdAt: new Date().toISOString()
        }
      ],
      assignedTo: [],
      createdBy: 'user3',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isShared: false,
      visibility: 'private'
    },
    {
      id: '4',
      name: 'Review competitor analysis',
      description: 'Analyze competitor websites and marketing strategies',
      dueToday: false,
      dueDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 10 days from now
      priority: 'High',
      tags: ['research', 'analysis'],
      comments: [],
      subtasks: [],
      completed: false,
      projectId: '1', // Website Redesign
      reminders: [],
      assignedTo: [],
      createdBy: 'user4',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isShared: false,
      visibility: 'private'
    },
    {
      id: '5',
      name: 'Plan team meeting agenda',
      description: 'Prepare agenda for weekly team sync meeting',
      dueToday: false,
      dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Tomorrow
      priority: 'Normal',
      tags: ['meeting', 'planning'],
      comments: [],
      subtasks: [],
      completed: false,
      projectId: undefined, // Inbox task
      reminders: [],
      assignedTo: [],
      createdBy: 'user5',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isShared: false,
      visibility: 'private'
    },
    {
      id: '6',
      name: 'Update project documentation',
      description: 'Update README and API documentation',
      dueToday: false,
      dueDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 5 days ago (overdue)
      priority: 'Low',
      tags: ['documentation'],
      comments: [],
      subtasks: [],
      completed: false,
      projectId: '2', // Mobile App Development
      reminders: [],
      assignedTo: [],
      createdBy: 'user6',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isShared: false,
      visibility: 'private'
    },
    {
      id: '7',
      name: 'Brainstorm new feature ideas',
      description: 'Generate ideas for next sprint features',
      dueToday: false,
      priority: 'Normal',
      tags: ['brainstorming', 'planning'],
      comments: [],
      subtasks: [],
      completed: false,
      projectId: '1', // Website Redesign
      reminders: [],
      assignedTo: [],
      createdBy: 'user7',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isShared: false,
      visibility: 'private'
    },
    {
      id: '8',
      name: 'Send client proposal',
      description: 'Finalize and send proposal to potential client',
      dueToday: false,
      dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 3 days from now
      priority: 'Urgent',
      tags: ['client', 'proposal'],
      comments: [],
      subtasks: [],
      completed: false,
      projectId: '3', // Marketing Campaign
      reminders: [],
      assignedTo: [],
      createdBy: 'user8',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isShared: false,
      visibility: 'private'
    },
    {
      id: '9',
      name: 'Test mobile app on different devices',
      description: 'Test app functionality on iOS and Android devices',
      dueToday: false,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
      priority: 'High',
      tags: ['testing', 'mobile'],
      comments: [],
      subtasks: [],
      completed: false,
      projectId: '2', // Mobile App Development
      reminders: [],
      assignedTo: [],
      createdBy: 'user9',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isShared: false,
      visibility: 'private'
    },
    {
      id: '10',
      name: 'Organize project files',
      description: 'Clean up and organize project file structure',
      dueToday: false,
      priority: 'Low',
      tags: ['organization'],
      comments: [],
      subtasks: [],
      completed: false,
      projectId: undefined, // Inbox task
      reminders: [],
      assignedTo: [],
      createdBy: 'user10',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isShared: false,
      visibility: 'private'
    }
  ]);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [documents, setDocuments] = useState<Document[]>([
    {
      id: 'doc_1',
      name: 'Project Requirements.pdf',
      originalName: 'Project Requirements.pdf',
      type: 'pdf',
      mimeType: 'application/pdf',
      size: 2.5 * 1024 * 1024, // 2.5 MB
      tags: ['requirements', 'project', 'documentation'],
      projectId: '1',
      uploadedBy: 'user1',
      uploadedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      lastModified: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      isShared: true,
      sharedWith: ['user2', 'user3'],
      visibility: 'team',
      comments: [],
      version: 1,
      description: 'Complete project requirements document',
      ocrText: 'This document contains the complete project requirements for the website redesign project. It includes detailed specifications for user interface design, functionality requirements, and technical implementation guidelines.',
      contentText: 'Project Requirements Document\n\n1. Overview\nThis document outlines the requirements for the website redesign project.\n\n2. Functional Requirements\n- User authentication system\n- Responsive design for mobile devices\n- Content management system\n- Search functionality\n\n3. Technical Requirements\n- React.js frontend\n- Node.js backend\n- MongoDB database\n- AWS hosting',
      previewData: {
        type: 'text',
        content: 'Project Requirements Document\n\n1. Overview\nThis document outlines the requirements for the website redesign project.\n\n2. Functional Requirements\n- User authentication system\n- Responsive design for mobile devices\n- Content management system\n- Search functionality\n\n3. Technical Requirements\n- React.js frontend\n- Node.js backend\n- MongoDB database\n- AWS hosting'
      },
      processingStatus: 'completed'
    },
    {
      id: 'doc_2',
      name: 'Homepage Mockup.png',
      originalName: 'Homepage Mockup.png',
      type: 'image',
      mimeType: 'image/png',
      size: 1.8 * 1024 * 1024, // 1.8 MB
      tags: ['design', 'mockup', 'homepage'],
      projectId: '1',
      uploadedBy: 'user2',
      uploadedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      lastModified: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      isShared: false,
      visibility: 'private',
      comments: [],
      version: 1,
      description: 'Design mockup for homepage',
      ocrText: 'Homepage Design Mockup\n\nNavigation Bar\n- Logo: Company Name\n- Menu Items: Home, About, Services, Contact\n\nHero Section\n- Headline: "Welcome to Our Platform"\n- Subtitle: "The best solution for your needs"\n- Call-to-Action Button: "Get Started"\n\nContent Sections\n- Features overview\n- Customer testimonials\n- Contact information',
      previewData: {
        type: 'image',
        imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop'
      },
      processingStatus: 'completed'
    },
    {
      id: 'doc_3',
      name: 'Budget Spreadsheet.xlsx',
      originalName: 'Budget Spreadsheet.xlsx',
      type: 'spreadsheet',
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      size: 0.5 * 1024 * 1024, // 0.5 MB
      tags: ['budget', 'finance', 'planning'],
      projectId: '2',
      uploadedBy: 'user1',
      uploadedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      lastModified: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      isShared: true,
      sharedWith: ['user2'],
      visibility: 'team',
      comments: [],
      version: 1,
      description: 'Project budget and financial planning',
      contentText: 'Budget,Amount,Category\nDevelopment,$50,000,Technical\nDesign,$15,000,Creative\nMarketing,$25,000,Promotion\nInfrastructure,$10,000,Technical\nTotal,$100,000,',
      previewData: {
        type: 'spreadsheet',
        spreadsheetData: {
          sheets: [{
            name: 'Budget',
            data: [
              ['Budget', 'Amount', 'Category'],
              ['Development', '$50,000', 'Technical'],
              ['Design', '$15,000', 'Creative'],
              ['Marketing', '$25,000', 'Promotion'],
              ['Infrastructure', '$10,000', 'Technical'],
              ['Total', '$100,000', '']
            ]
          }]
        }
      },
      processingStatus: 'completed'
    },
    {
      id: 'doc_4',
      name: 'API Documentation.md',
      originalName: 'API Documentation.md',
      type: 'code',
      mimeType: 'text/markdown',
      size: 0.1 * 1024 * 1024, // 0.1 MB
      tags: ['api', 'documentation', 'technical'],
      projectId: '3',
      uploadedBy: 'user3',
      uploadedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
      lastModified: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
      isShared: false,
      visibility: 'private',
      comments: [],
      version: 1,
      description: 'API documentation and endpoints',
      contentText: '# API Documentation\n\n## Authentication\nAll API requests require authentication using Bearer tokens.\n\n## Endpoints\n\n### GET /api/users\nRetrieve list of users\n\n### POST /api/users\nCreate a new user\n\n### PUT /api/users/:id\nUpdate user information\n\n### DELETE /api/users/:id\nDelete a user',
      previewData: {
        type: 'code',
        content: '# API Documentation\n\n## Authentication\nAll API requests require authentication using Bearer tokens.\n\n## Endpoints\n\n### GET /api/users\nRetrieve list of users\n\n### POST /api/users\nCreate a new user\n\n### PUT /api/users/:id\nUpdate user information\n\n### DELETE /api/users/:id\nDelete a user',
        codeLanguage: 'Markdown'
      },
      processingStatus: 'completed'
    }
  ]);
  const [projects, setProjects] = useState<Project[]>([
    {
      id: '1',
      name: 'Website Redesign',
      description: 'Complete overhaul of company website',
      color: '#3B82F6',
      isFavorite: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      taskCount: 8,
      completedTaskCount: 3,
      createdBy: 'user1',
      teamMembers: [],
      isShared: false,
      visibility: 'private',
      settings: {
        allowComments: true,
        allowTaskAssignment: true,
        allowExternalSharing: false,
        requireApproval: false
      }
    },
    {
      id: '2',
      name: 'Mobile App Development',
      description: 'Build iOS and Android apps',
      color: '#10B981',
      isFavorite: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      taskCount: 12,
      completedTaskCount: 5,
      createdBy: 'user2',
      teamMembers: [],
      isShared: false,
      visibility: 'private',
      settings: {
        allowComments: true,
        allowTaskAssignment: true,
        allowExternalSharing: false,
        requireApproval: false
      }
    },
    {
      id: '3',
      name: 'Marketing Campaign',
      description: 'Q4 marketing initiatives',
      color: '#F59E0B',
      isFavorite: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      taskCount: 6,
      completedTaskCount: 2,
      createdBy: 'user3',
      teamMembers: [],
      isShared: false,
      visibility: 'private',
      settings: {
        allowComments: true,
        allowTaskAssignment: true,
        allowExternalSharing: false,
        requireApproval: false
      }
    }
  ]);

  // Notification state
  const [notificationSettings, setNotificationSettings] = useState({
    enabled: false,
    permission: 'default' as NotificationPermission,
    dueDateReminders: true,
    customReminders: true,
    soundEnabled: true,
    desktopNotifications: true
  });

  // Collaboration state
  const [users, setUsers] = useState<User[]>([
    {
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      role: 'owner',
      isOnline: true,
      lastSeen: new Date().toISOString()
    },
    {
      id: 'user2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      role: 'admin',
      isOnline: true,
      lastSeen: new Date().toISOString()
    },
    {
      id: 'user3',
      name: 'Mike Johnson',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      role: 'member',
      isOnline: false,
      lastSeen: new Date(Date.now() - 30 * 60 * 1000).toISOString() // 30 minutes ago
    },
    {
      id: 'user4',
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      role: 'member',
      isOnline: true,
      lastSeen: new Date().toISOString()
    }
  ]);

  const [currentUser] = useState<User>(users[0]); // Current user is John Doe

  // Check notification permission on mount
  useEffect(() => {
    if ('Notification' in window) {
      setNotificationSettings(prev => ({
        ...prev,
        permission: Notification.permission
      }));
    }
  }, []);

  // Request notification permission
  const requestNotificationPermission = async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      const permission = await Notification.requestPermission();
      setNotificationSettings(prev => ({
        ...prev,
        permission,
        enabled: permission === 'granted'
      }));
    }
  };

  const handleAddTask = (task: Omit<Task, 'id'>) => {
    const newTaskId = Date.now().toString();
    const newTask = { 
      ...task, 
      id: newTaskId, 
      comments: [], 
      completed: false,
      reminders: task.reminders || []
    };
    
    setTasks(prev => [newTask, ...prev]);
    
    // Add reminders if any
    if (task.reminders && task.reminders.length > 0) {
      task.reminders.forEach(reminder => {
        handleAddReminder(newTaskId, reminder);
      });
    }
    
    setShowTaskForm(false);
  };

  const handleUpdateTask = (updated: Task) => {
    setTasks(prev => prev.map(t => t.id === updated.id ? { ...t, ...updated, comments: updated.comments || t.comments } : t));
    setSelectedTask(updated);
  };

  const handleToggleComplete = (task: Task, completed: boolean) => {
    setTasks(prev => prev.map(t => t.id === task.id ? { ...t, completed } : t));
  };

  const handleUpdateTaskStatus = (taskId: string, newStatus: string) => {
    setTasks(prev => prev.map(t => 
      t.id === taskId 
        ? { 
            ...t, 
            status: newStatus as 'todo' | 'in-progress' | 'review' | 'done' | 'backlog',
            completed: newStatus === 'done',
            updatedAt: new Date().toISOString()
          } 
        : t
    ));
  };

  const handleAddProject = (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt' | 'taskCount' | 'completedTaskCount'>) => {
    const newProject: Project = {
      ...project,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      taskCount: 0,
      completedTaskCount: 0,
      createdBy: 'user1',
      teamMembers: [],
      isShared: false,
      visibility: 'private',
      settings: {
        allowComments: true,
        allowTaskAssignment: true,
        allowExternalSharing: false,
        requireApproval: false
      }
    };
    setProjects(prev => [...prev, newProject]);
  };

  const handleUpdateProject = (updated: Project) => {
    setProjects(prev => prev.map(p => p.id === updated.id ? { ...p, ...updated, updatedAt: new Date().toISOString() } : p));
  };

  const handleDeleteProject = (projectId: string) => {
    setProjects(prev => prev.filter(p => p.id !== projectId));
    // Move tasks from deleted project to inbox
    setTasks(prev => prev.map(t => t.projectId === projectId ? { ...t, projectId: undefined } : t));
  };

  const handleToggleProjectFavorite = (projectId: string) => {
    setProjects(prev => prev.map(p => p.id === projectId ? { ...p, isFavorite: !p.isFavorite } : p));
  };

  // Reminder management functions
  const handleAddReminder = (taskId: string, reminder: Omit<Reminder, 'id' | 'taskId' | 'createdAt'>) => {
    const newReminder: Reminder = {
      ...reminder,
      id: Date.now().toString(),
      taskId,
      createdAt: new Date().toISOString()
    };
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? { ...task, reminders: [...(task.reminders || []), newReminder] }
        : task
    ));
    setSelectedTask(prev =>
      prev && prev.id === taskId
        ? { ...prev, reminders: [...(prev.reminders || []), newReminder] }
        : prev
    );
  };

  const handleUpdateReminder = (taskId: string, reminderId: string, updates: Partial<Reminder>) => {
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? {
            ...task,
            reminders: (task.reminders || []).map(reminder =>
              reminder.id === reminderId
                ? { ...reminder, ...updates }
                : reminder
            )
          }
        : task
    ));
    setSelectedTask(prev =>
      prev && prev.id === taskId
        ? {
            ...prev,
            reminders: (prev.reminders || []).map(reminder =>
              reminder.id === reminderId
                ? { ...reminder, ...updates }
                : reminder
            )
          }
        : prev
    );
  };

  const handleDeleteReminder = (taskId: string, reminderId: string) => {
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? {
            ...task,
            reminders: (task.reminders || []).filter(reminder => reminder.id !== reminderId)
          }
        : task
    ));
    setSelectedTask(prev =>
      prev && prev.id === taskId
        ? {
            ...prev,
            reminders: (prev.reminders || []).filter(reminder => reminder.id !== reminderId)
          }
        : prev
    );
  };

  const handleUpdateNotificationSettings = (settings: Partial<typeof notificationSettings>) => {
    setNotificationSettings(prev => ({ ...prev, ...settings }));
  };

  // Collaboration handlers
  const handleAssignTask = (taskId: string, userIds: string[]) => {
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? { 
            ...task, 
            assignedTo: userIds,
            updatedBy: currentUser.id,
            updatedAt: new Date().toISOString()
          }
        : task
    ));
    setSelectedTask(prev =>
      prev && prev.id === taskId
        ? { 
            ...prev, 
            assignedTo: userIds,
            updatedBy: currentUser.id,
            updatedAt: new Date().toISOString()
          }
        : prev
    );
  };

  const handleAddComment = (taskId: string, comment: Omit<Comment, 'id' | 'timestamp' | 'isEdited'>) => {
    const newComment: Comment = {
      ...comment,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      isEdited: false
    };
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? { 
            ...task, 
            comments: [...(task.comments || []), newComment],
            updatedBy: currentUser.id,
            updatedAt: new Date().toISOString()
          }
        : task
    ));
    setSelectedTask(prev =>
      prev && prev.id === taskId
        ? { 
            ...prev, 
            comments: [...(prev.comments || []), newComment],
            updatedBy: currentUser.id,
            updatedAt: new Date().toISOString()
          }
        : prev
    );
  };

  const handleUpdateComment = (taskId: string, commentId: string, updates: Partial<Comment>) => {
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? {
            ...task,
            comments: (task.comments || []).map(comment =>
              comment.id === commentId
                ? { ...comment, ...updates, isEdited: true, editedAt: new Date().toISOString() }
                : comment
            )
          }
        : task
    ));
    setSelectedTask(prev =>
      prev && prev.id === taskId
        ? {
            ...prev,
            comments: (prev.comments || []).map(comment =>
              comment.id === commentId
                ? { ...comment, ...updates, isEdited: true, editedAt: new Date().toISOString() }
                : comment
            )
          }
        : prev
    );
  };

  const handleDeleteComment = (taskId: string, commentId: string) => {
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? {
            ...task,
            comments: (task.comments || []).filter(comment => comment.id !== commentId)
          }
        : task
    ));
    setSelectedTask(prev =>
      prev && prev.id === taskId
        ? {
            ...prev,
            comments: (prev.comments || []).filter(comment => comment.id !== commentId)
          }
        : prev
    );
  };

  const handleShareTask = (taskId: string, userIds: string[], visibility: 'private' | 'team' | 'public') => {
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? { 
            ...task, 
            sharedWith: userIds,
            isShared: userIds.length > 0 || visibility !== 'private',
            visibility,
            updatedBy: currentUser.id,
            updatedAt: new Date().toISOString()
          }
        : task
    ));
    setSelectedTask(prev =>
      prev && prev.id === taskId
        ? { 
            ...prev, 
            sharedWith: userIds,
            isShared: userIds.length > 0 || visibility !== 'private',
            visibility,
            updatedBy: currentUser.id,
            updatedAt: new Date().toISOString()
          }
        : prev
    );
  };

  const handleAddTeamMember = (projectId: string, userId: string, role: 'admin' | 'member' | 'viewer') => {
    const permissions = {
      canEdit: role === 'admin' || role === 'member',
      canDelete: role === 'admin',
      canAssign: role === 'admin' || role === 'member',
      canComment: true,
      canShare: role === 'admin' || role === 'member'
    };

    const newMember: TeamMember = {
      userId,
      projectId,
      role,
      joinedAt: new Date().toISOString(),
      permissions
    };

    setProjects(prev => prev.map(project =>
      project.id === projectId
        ? { 
            ...project, 
            teamMembers: [...(project.teamMembers || []), newMember],
            isShared: true,
            updatedAt: new Date().toISOString()
          }
        : project
    ));
  };

  const handleRemoveTeamMember = (projectId: string, userId: string) => {
    setProjects(prev => prev.map(project =>
      project.id === projectId
        ? { 
            ...project, 
            teamMembers: (project.teamMembers || []).filter(member => member.userId !== userId),
            updatedAt: new Date().toISOString()
          }
        : project
    ));
  };

  const handleUpdateTeamMemberRole = (projectId: string, userId: string, newRole: 'admin' | 'member' | 'viewer') => {
    const permissions = {
      canEdit: newRole === 'admin' || newRole === 'member',
      canDelete: newRole === 'admin',
      canAssign: newRole === 'admin' || newRole === 'member',
      canComment: true,
      canShare: newRole === 'admin' || newRole === 'member'
    };

    setProjects(prev => prev.map(project =>
      project.id === projectId
        ? { 
            ...project, 
            teamMembers: (project.teamMembers || []).map(member =>
              member.userId === userId
                ? { ...member, role: newRole, permissions }
                : member
            ),
            updatedAt: new Date().toISOString()
          }
        : project
    ));
  };

  const handleUpdateProjectSettings = (projectId: string, settings: Partial<Project['settings']>) => {
    setProjects(prev => prev.map(project =>
      project.id === projectId
        ? { 
            ...project, 
            settings: { ...project.settings, ...settings },
            updatedAt: new Date().toISOString()
          }
        : project
    ));
  };

  // Document management handlers
  const handleAddDocument = (document: Omit<Document, 'id' | 'uploadedAt' | 'lastModified' | 'version'>) => {
    const newDocument: Document = {
      ...document,
      id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      uploadedAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      version: 1
    };
    setDocuments(prev => [newDocument, ...prev]);
    return newDocument;
  };

  const handleUpdateDocument = (documentId: string, updates: Partial<Document>) => {
    setDocuments(prev => prev.map(doc =>
      doc.id === documentId
        ? { 
            ...doc, 
            ...updates, 
            lastModified: new Date().toISOString(),
            version: doc.version + 1
          }
        : doc
    ));
  };

  const handleDeleteDocument = (documentId: string) => {
    setDocuments(prev => prev.filter(doc => doc.id !== documentId));
  };

  const handleShareDocument = (documentId: string, userIds: string[], visibility: 'private' | 'team' | 'public') => {
    setDocuments(prev => prev.map(doc =>
      doc.id === documentId
        ? { 
            ...doc, 
            isShared: true,
            sharedWith: userIds,
            visibility,
            lastModified: new Date().toISOString()
          }
        : doc
    ));
  };

  const handleAddDocumentComment = (documentId: string, comment: Omit<Comment, 'id' | 'timestamp' | 'isEdited'>) => {
    const newComment: Comment = {
      ...comment,
      id: `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      isEdited: false
    };

    setDocuments(prev => prev.map(doc =>
      doc.id === documentId
        ? { 
            ...doc, 
            comments: [...(doc.comments || []), newComment],
            lastModified: new Date().toISOString()
          }
        : doc
    ));
  };

  // Calculate project stats
  const projectsWithStats = projects.map(project => {
    const projectTasks = tasks.filter(t => t.projectId === project.id);
    return {
      ...project,
      taskCount: projectTasks.length,
      completedTaskCount: projectTasks.filter(t => t.completed).length
    };
  });

  const [showChatPanel, setShowChatPanel] = useState(false);

  return (
    <BrowserRouter>
      <div>
        <Header />
        <div className="flex">
          <Sidebar 
            onAddTask={() => setShowTaskForm(true)} 
            projects={projectsWithStats}
            onToggleProjectFavorite={handleToggleProjectFavorite}
            onChatClick={() => setShowChatPanel(true)}
          />
          <main
            className="flex-1 p-6"
            style={{ marginLeft: DRAWER_WIDTH, paddingTop: APPBAR_HEIGHT + 16 }}
          >
            <Routes>
              <Route path="/" element={<Inbox tasks={tasks} projects={projectsWithStats} onTaskClick={setSelectedTask} onToggleComplete={handleToggleComplete} />} />
              <Route path="/inbox" element={<Inbox tasks={tasks} projects={projectsWithStats} onTaskClick={setSelectedTask} onToggleComplete={handleToggleComplete} />} />
              <Route path="/today" element={<Today tasks={tasks} projects={projectsWithStats} onTaskClick={setSelectedTask} onToggleComplete={handleToggleComplete} />} />
              <Route path="/upcoming" element={<Upcoming tasks={tasks} projects={projectsWithStats} onTaskClick={setSelectedTask} onToggleComplete={handleToggleComplete} />} />
              <Route path="/projects" element={
                <Projects 
                  projects={projectsWithStats}
                  tasks={tasks}
                  onAddProject={handleAddProject}
                  onUpdateProject={handleUpdateProject}
                  onDeleteProject={handleDeleteProject}
                  onToggleProjectFavorite={handleToggleProjectFavorite}
                  onTaskClick={setSelectedTask}
                  onToggleComplete={handleToggleComplete}
                  onUpdateTaskStatus={handleUpdateTaskStatus}
                />
              } />
              <Route path="/projects/:id" element={
                <Projects 
                  projects={projectsWithStats}
                  tasks={tasks}
                  onAddProject={handleAddProject}
                  onUpdateProject={handleUpdateProject}
                  onDeleteProject={handleDeleteProject}
                  onToggleProjectFavorite={handleToggleProjectFavorite}
                  onTaskClick={setSelectedTask}
                  onToggleComplete={handleToggleComplete}
                  onUpdateTaskStatus={handleUpdateTaskStatus}
                />
              } />
              <Route path="/objects" element={<Objects />} />
              <Route path="/settings" element={
                <Settings 
                  notificationSettings={notificationSettings}
                  onUpdateNotificationSettings={handleUpdateNotificationSettings}
                  onRequestPermission={requestNotificationPermission}
                />
              } />
              <Route path="/task/:id" element={<TaskDetails />} />
              <Route path="/login" element={<Login />} />
              <Route path="/mentions-test" element={<MentionsTest />} />
              <Route path="/users" element={<Users />} />
              <Route path="/documents" element={
                <Documents 
                  documents={documents}
                  projects={projectsWithStats}
                  onAddDocument={handleAddDocument}
                  onUpdateDocument={handleUpdateDocument}
                  onDeleteDocument={handleDeleteDocument}
                  onShareDocument={handleShareDocument}
                  onAddDocumentComment={handleAddDocumentComment}
                  users={users}
                />
              } />
              <Route path="/notes/*" element={
                <NotesProvider>
                  <Routes>
                    <Route path="" element={<NotesPage />} />
                    <Route path=":id" element={<NoteDetailPage />} />
                  </Routes>
                </NotesProvider>
              } />
            </Routes>
          </main>
        </div>
        {/* TaskForm Modal */}
        {showTaskForm && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
            <div className="relative">
              <button
                className="absolute top-2 right-2 text-gray-400 hover:text-gray-200 text-2xl z-10"
                onClick={() => setShowTaskForm(false)}
                aria-label="Close"
              >
                &times;
              </button>
              <TaskForm 
                onAddTask={handleAddTask} 
                projects={projectsWithStats} 
                onCancel={() => setShowTaskForm(false)}
                onAddReminder={handleAddReminder}
              />
            </div>
          </div>
        )}
        {/* Task Detail Modal */}
        {selectedTask && (
          <TaskDetailModal 
            task={selectedTask} 
            onClose={() => setSelectedTask(null)} 
            onUpdateTask={handleUpdateTask} 
            projects={projectsWithStats}
            onAddReminder={handleAddReminder}
            onUpdateReminder={handleUpdateReminder}
            onDeleteReminder={handleDeleteReminder}
            // Collaboration props
            users={users}
            currentUser={currentUser}
            onAssignTask={handleAssignTask}
            onAddComment={handleAddComment}
            onUpdateComment={handleUpdateComment}
            onDeleteComment={handleDeleteComment}
            onShareTask={handleShareTask}
          />
        )}
        {/* Notification Manager */}
        <NotificationManager 
          tasks={tasks}
          notificationSettings={notificationSettings}
          onUpdateReminder={handleUpdateReminder}
        />
        {/* Chat Panel */}
        {showChatPanel && (
          <div className="fixed top-0 right-0 h-full w-[420px] z-40 shadow-lg bg-gray-900 border-l border-gray-800">
            <ChatInterface
              isOpen={showChatPanel}
              onClose={() => setShowChatPanel(false)}
              currentProjectId={undefined}
              currentTaskId={undefined}
              currentUserId={"user1"}
              users={users}
              projects={projects}
              tasks={tasks}
            />
          </div>
        )}
      </div>
    </BrowserRouter>
  );
};

export default App;
