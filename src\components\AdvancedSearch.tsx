import React, { useState, useEffect, useMemo } from 'react';
import { FiSearch, FiX, <PERSON>Filter, <PERSON>Eye, FiEyeOff, FiFile, FiImage, FiFileText, FiBarChart2, FiCode, FiArchive } from 'react-icons/fi';
import contentSearchService, { SearchResult, SearchOptions } from '../utils/contentSearch';
import type { Document } from '../App';

interface AdvancedSearchProps {
  documents: Document[];
  onSearchResults: (results: SearchResult[]) => void;
  onDocumentClick?: (documentId: string) => void;
  className?: string;
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  documents,
  onSearchResults,
  onDocumentClick,
  className = ''
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Search options
  const [searchOptions, setSearchOptions] = useState<SearchOptions>({
    caseSensitive: false,
    includeOCR: true,
    includeTags: true,
    includeDescription: true,
    maxResults: 50,
    minRelevanceScore: 0.1
  });

  // Index documents when they change
  useEffect(() => {
    documents.forEach(doc => {
      contentSearchService.indexDocument(doc);
    });
  }, [documents]);

  // Search suggestions
  useEffect(() => {
    if (searchQuery.length > 2) {
      const newSuggestions = contentSearchService.getSuggestions(searchQuery, 5);
      setSuggestions(newSuggestions);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [searchQuery]);

  // Perform search
  const performSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      onSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const results = contentSearchService.search(query, searchOptions);
      setSearchResults(results);
      onSearchResults(results);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
      onSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, searchOptions]);

  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion);
    setShowSuggestions(false);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    onSearchResults([]);
    setShowSuggestions(false);
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf': return <FiFile className="text-red-500" />;
      case 'image': return <FiImage className="text-green-500" />;
      case 'document': return <FiFileText className="text-blue-500" />;
      case 'spreadsheet': return <FiBarChart2 className="text-green-600" />;
      case 'code': return <FiCode className="text-purple-500" />;
      case 'archive': return <FiArchive className="text-gray-500" />;
      default: return <FiFile className="text-gray-400" />;
    }
  };

  const getSearchStats = () => {
    return contentSearchService.getSearchStats();
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="relative">
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search documents, content, OCR text..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-red-500 focus:border-transparent"
          />
          {searchQuery && (
            <button
              onClick={handleClearSearch}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <FiX size={16} />
            </button>
          )}
        </div>

        {/* Search Suggestions */}
        {showSuggestions && suggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="w-full px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 first:rounded-t-lg last:rounded-b-lg"
              >
                <FiSearch className="inline mr-2 text-gray-400" size={14} />
                {suggestion}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Advanced Options Toggle */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
          className="flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          <FiFilter className="mr-2" size={14} />
          Advanced Search Options
        </button>

        {/* Search Stats */}
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {getSearchStats().totalDocuments} documents indexed
        </div>
      </div>

      {/* Advanced Search Options */}
      {showAdvancedOptions && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Case Sensitive */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={searchOptions.caseSensitive}
                onChange={(e) => setSearchOptions(prev => ({ ...prev, caseSensitive: e.target.checked }))}
                className="rounded border-gray-300 dark:border-gray-600"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">Case Sensitive</span>
            </label>

            {/* Include OCR */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={searchOptions.includeOCR}
                onChange={(e) => setSearchOptions(prev => ({ ...prev, includeOCR: e.target.checked }))}
                className="rounded border-gray-300 dark:border-gray-600"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">Include OCR Text</span>
            </label>

            {/* Include Tags */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={searchOptions.includeTags}
                onChange={(e) => setSearchOptions(prev => ({ ...prev, includeTags: e.target.checked }))}
                className="rounded border-gray-300 dark:border-gray-600"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">Include Tags</span>
            </label>

            {/* Include Description */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={searchOptions.includeDescription}
                onChange={(e) => setSearchOptions(prev => ({ ...prev, includeDescription: e.target.checked }))}
                className="rounded border-gray-300 dark:border-gray-600"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">Include Description</span>
            </label>

            {/* Max Results */}
            <div>
              <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                Max Results
              </label>
              <select
                value={searchOptions.maxResults}
                onChange={(e) => setSearchOptions(prev => ({ ...prev, maxResults: parseInt(e.target.value) }))}
                className="w-full px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
            </div>

            {/* Min Relevance Score */}
            <div>
              <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                Min Relevance
              </label>
              <select
                value={searchOptions.minRelevanceScore}
                onChange={(e) => setSearchOptions(prev => ({ ...prev, minRelevanceScore: parseFloat(e.target.value) }))}
                className="w-full px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value={0.1}>Low</option>
                <option value={0.3}>Medium</option>
                <option value={0.5}>High</option>
                <option value={0.7}>Very High</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Search Results */}
      {searchQuery && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Search Results
              {isSearching && (
                <span className="ml-2 text-sm text-gray-500">
                  <div className="animate-spin inline-block w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full"></div>
                  Searching...
                </span>
              )}
            </h3>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {searchResults.length} results
            </span>
          </div>

          {searchResults.length > 0 ? (
            <div className="space-y-3">
              {searchResults.map((result) => (
                <div
                  key={result.documentId}
                  className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => onDocumentClick?.(result.documentId)}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {getFileIcon(result.documentType)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {result.documentName}
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {result.documentType.toUpperCase()} • {result.matches.length} matches • 
                        Relevance: {Math.round(result.relevanceScore * 10) / 10}
                      </p>
                      <div 
                        className="text-sm text-gray-600 dark:text-gray-300 mt-2"
                        dangerouslySetInnerHTML={{ __html: result.preview }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : !isSearching ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <FiSearch className="mx-auto h-8 w-8 mb-2" />
              <p>No documents found matching "{searchQuery}"</p>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default AdvancedSearch; 