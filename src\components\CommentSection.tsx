import React, { useState, useEffect } from 'react';
import { FiMessageSquare, FiEdit2, FiTrash2, <PERSON>CornerUpLeft, FiAtSign } from 'react-icons/fi';
import UserAvatar from './UserAvatar';
import MentionsInput from './MentionsInput';
import { FormattedText } from '../utils/textFormatter';
import type { Comment, User } from '../App';

interface CommentSectionProps {
  comments: Comment[];
  users: User[];
  currentUser: User;
  onAddComment: (comment: Omit<Comment, 'id' | 'timestamp' | 'isEdited'>) => void;
  onUpdateComment: (commentId: string, updates: Partial<Comment>) => void;
  onDeleteComment: (commentId: string) => void;
  className?: string;
}

const CommentSection: React.FC<CommentSectionProps> = ({
  comments,
  users,
  currentUser,
  onAddComment,
  onUpdateComment,
  onDeleteComment,
  className = ''
}) => {
  const [newComment, setNewComment] = useState('');
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editText, setEditText] = useState('');
  const [replyToCommentId, setReplyToCommentId] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');

  useEffect(() => {
    setNewComment('');
  }, [comments]);

  const handleSubmitComment = () => {
    if (newComment.trim()) {
      onAddComment({
        text: newComment,
        author: currentUser.name,
        authorId: currentUser.id,
        mentions: []
      });
      setNewComment('');
    }
  };

  const handleStartEdit = (comment: Comment) => {
    setEditingCommentId(comment.id);
    setEditText(comment.text);
  };

  const handleSaveEdit = () => {
    if (editText.trim() && editingCommentId) {
      onUpdateComment(editingCommentId, {
        text: editText,
        mentions: []
      });
      setEditingCommentId(null);
      setEditText('');
    }
  };

  const handleCancelEdit = () => {
    setEditingCommentId(null);
    setEditText('');
  };

  const handleStartReply = (commentId: string) => {
    setReplyToCommentId(commentId);
    setReplyText('');
  };

  const handleSubmitReply = () => {
    if (replyText.trim() && replyToCommentId) {
      const parentComment = comments.find(c => c.id === replyToCommentId);
      if (parentComment) {
        const reply: Comment = {
          id: Date.now().toString(),
          text: replyText,
          author: currentUser.name,
          authorId: currentUser.id,
          timestamp: new Date().toISOString(),
          mentions: [],
          replies: [],
          isEdited: false
        };
        onUpdateComment(replyToCommentId, {
          replies: [...(parentComment.replies || []), reply]
        });
      }
      setReplyToCommentId(null);
      setReplyText('');
    }
  };

  if (!currentUser || !Array.isArray(users) || !Array.isArray(comments)) {
    return (
      <div className={className}>
        <div className="text-center py-8 text-gray-400">
          <FiMessageSquare className="w-8 h-8 mx-auto mb-2" />
          <p>Comments not available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="flex items-center gap-2 mb-4">
        <FiMessageSquare className="text-gray-400" />
        <h3 className="text-lg font-semibold text-gray-100">Comments</h3>
        <span className="text-sm text-gray-400">({comments.length})</span>
      </div>
      {/* Add Comment */}
      <div className="mb-6">
        <div className="flex gap-3">
          <UserAvatar user={currentUser} size="md" />
          <div className="flex-1">
            <MentionsInput
              users={users}
              value={newComment}
              onChange={setNewComment}
              placeholder="Add a comment... Use @ to mention team members"
              rows={3}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-sm text-gray-300 placeholder-gray-400 resize-none focus:outline-none focus:border-blue-500"
            />
            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center gap-1 text-xs text-gray-400">
                <FiAtSign className="w-3 h-3" />
                <span>Use @ to mention team members</span>
              </div>
              <button
                onClick={handleSubmitComment}
                disabled={!newComment || typeof newComment !== 'string' || !newComment.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Post Comment
              </button>
            </div>
          </div>
        </div>
      </div>
      {/* Comments List */}
      <div className="space-y-4">
        {comments.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            <FiMessageSquare className="w-8 h-8 mx-auto mb-2" />
            <p>No comments yet. Be the first to comment!</p>
          </div>
        ) : (
          comments.map(comment => (
            <div key={comment.id} className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <UserAvatar user={{ id: comment.authorId, name: comment.author, email: '', role: 'member', isOnline: false, lastSeen: '' }} size="sm" />
                <span className="text-gray-200 font-medium">{comment.author}</span>
                <span className="text-xs text-gray-400">{new Date(comment.timestamp).toLocaleString()}</span>
                {comment.isEdited && <span className="text-xs text-yellow-400 ml-2">(edited)</span>}
              </div>
              <div className="text-gray-300 mb-2">
                <FormattedText text={comment.text} users={users} />
              </div>
              <div className="flex gap-2 text-xs text-gray-400">
                <button onClick={() => handleStartEdit(comment)} className="hover:text-blue-400 flex items-center gap-1"><FiEdit2 />Edit</button>
                <button onClick={() => onDeleteComment(comment.id)} className="hover:text-red-400 flex items-center gap-1"><FiTrash2 />Delete</button>
                <button onClick={() => handleStartReply(comment.id)} className="hover:text-green-400 flex items-center gap-1"><FiCornerUpLeft />Reply</button>
              </div>
              {/* Edit Mode */}
              {editingCommentId === comment.id && (
                <div className="mt-3 space-y-2">
                  <MentionsInput
                    users={users}
                    value={editText}
                    onChange={setEditText}
                    placeholder="Edit your comment..."
                    rows={2}
                    className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-gray-300 placeholder-gray-400 resize-none"
                  />
                  <div className="flex gap-2">
                    <button onClick={handleSaveEdit} className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">Save</button>
                    <button onClick={handleCancelEdit} className="px-3 py-1 bg-gray-600 text-gray-300 rounded text-sm hover:bg-gray-700">Cancel</button>
                  </div>
                </div>
              )}
              {/* Reply Mode */}
              {replyToCommentId === comment.id && (
                <div className="mt-3 space-y-2">
                  <MentionsInput
                    users={users}
                    value={replyText}
                    onChange={setReplyText}
                    placeholder="Write a reply..."
                    rows={2}
                    className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-gray-300 placeholder-gray-400 resize-none"
                  />
                  <div className="flex gap-2">
                    <button onClick={handleSubmitReply} className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">Reply</button>
                    <button onClick={() => setReplyToCommentId(null)} className="px-3 py-1 bg-gray-600 text-gray-300 rounded text-sm hover:bg-gray-700">Cancel</button>
                  </div>
                </div>
              )}
              {/* Replies */}
              {comment.replies && comment.replies.length > 0 && (
                <div className="mt-3 space-y-3">
                  {comment.replies.map(reply => (
                    <div key={reply.id} className="ml-6 bg-gray-700 rounded p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <UserAvatar user={{ id: reply.authorId, name: reply.author, email: '', role: 'member', isOnline: false, lastSeen: '' }} size="sm" />
                        <span className="text-gray-200 font-medium">{reply.author}</span>
                        <span className="text-xs text-gray-400">{new Date(reply.timestamp).toLocaleString()}</span>
                        {reply.isEdited && <span className="text-xs text-yellow-400 ml-2">(edited)</span>}
                      </div>
                      <div className="text-gray-300"><FormattedText text={reply.text} users={users} /></div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default CommentSection; 