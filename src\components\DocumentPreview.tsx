import React, { useState, useEffect } from 'react';
import { FiX, FiDownload, FiMaximize2, FiMinimize2, FiRotateCw, FiZoomIn, FiZoomOut, FiFile, FiImage, FiFileText, FiBarChart2, FiCode, FiArchive, FiTrash2 } from 'react-icons/fi';
import type { Document } from '../App';
import { Document as PDFDocument, Page, pdfjs } from 'react-pdf';
pdfjs.GlobalWorkerOptions.workerSrc = `${process.env.PUBLIC_URL}/pdf.worker.js`;

interface DocumentPreviewProps {
  document: Document;
  isOpen: boolean;
  onClose: () => void;
  onDownload?: (document: Document) => void;
  onDelete?: (document: Document) => void;
  onUpdateTags?: (documentId: string, tags: string[]) => void;
  onUpdateAccess?: (documentId: string, access: { sharedWith: string[]; visibility: 'private' | 'team' | 'public' }) => void;
  users?: { id: string; name: string; avatar?: string }[];
}

const DocumentPreview: React.FC<DocumentPreviewProps> = ({
  document,
  isOpen,
  onClose,
  onDownload,
  onDelete,
  onUpdateTags,
  onUpdateAccess,
  users = []
}) => {
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [activeSheet, setActiveSheet] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [pdfPage, setPdfPage] = useState(1);
  const [pdfNumPages, setPdfNumPages] = useState(1);
  const [tagInput, setTagInput] = useState('');
  const [tags, setTags] = useState(document.tags || []);
  const [visibility, setVisibility] = useState(document.visibility || 'private');
  const [sharedWith, setSharedWith] = useState(document.sharedWith || []);

  useEffect(() => {
    if (isOpen) {
      setZoom(1);
      setRotation(0);
      setIsFullscreen(false);
      setIsLoading(true);
    }
    setTags(document.tags || []);
    setVisibility(document.visibility || 'private');
    setSharedWith(document.sharedWith || []);
  }, [isOpen, document]);

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.25, 3));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.25, 0.25));
  const handleRotate = () => setRotation(prev => (prev + 90) % 360);
  const handleReset = () => {
    setZoom(1);
    setRotation(0);
  };

  const handleDownload = () => {
    if (onDownload) {
      onDownload(document);
    } else if (document.type === 'pdf' && document.url) {
      // Download PDF using blob URL
      const link = window.document.createElement('a');
      link.href = document.url;
      link.download = document.name.endsWith('.pdf') ? document.name : document.name + '.pdf';
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
    } else if (document.type === 'image' && document.previewData?.imageUrl) {
      const link = window.document.createElement('a');
      link.href = document.previewData.imageUrl;
      link.download = document.name;
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
    } else if (document.previewData?.content) {
      // For text-based documents, create a blob from the content
      const blob = new Blob([document.previewData.content], { 
        type: document.mimeType || 'text/plain' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = document.name;
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }
  };

  const getFileIcon = (type: Document['type']) => {
    switch (type) {
      case 'pdf': return <FiFile className="text-red-500" />;
      case 'image': return <FiImage className="text-green-500" />;
      case 'document': return <FiFileText className="text-blue-500" />;
      case 'spreadsheet': return <FiBarChart2 className="text-green-600" />;
      case 'code': return <FiCode className="text-purple-500" />;
      case 'archive': return <FiArchive className="text-gray-500" />;
      default: return <FiFile className="text-gray-400" />;
    }
  };

  const renderImagePreview = () => (
    <div className="flex items-center justify-center bg-gray-900 min-h-96 relative">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-75">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      )}
      <img
        src={document.previewData?.imageUrl || document.url || '/placeholder-image.png'}
        alt={document.name}
        className="max-w-full max-h-full object-contain"
        style={{
          transform: `scale(${zoom}) rotate(${rotation}deg)`,
          transition: 'transform 0.2s ease-in-out'
        }}
        onLoad={() => setIsLoading(false)}
        onError={() => setIsLoading(false)}
      />
    </div>
  );

  const handlePdfZoomIn = () => setZoom(z => Math.min(z + 0.25, 3));
  const handlePdfZoomOut = () => setZoom(z => Math.max(z - 0.25, 0.5));
  const handlePdfPageChange = (offset: number) => setPdfPage(p => Math.max(1, Math.min(pdfNumPages, p + offset)));

  const handleAddTag = () => {
    const newTag = tagInput.trim();
    if (newTag && !tags.includes(newTag)) {
      const updated = [...tags, newTag];
      setTags(updated);
      setTagInput('');
      onUpdateTags && onUpdateTags(document.id, updated);
    }
  };
  const handleRemoveTag = (tag: string) => {
    const updated = tags.filter(t => t !== tag);
    setTags(updated);
    onUpdateTags && onUpdateTags(document.id, updated);
  };

  const accessUsers = document.sharedWith && document.sharedWith.length > 0
    ? users.filter(u => document.sharedWith?.includes(u.id))
    : [];
  const visibilityLabel = document.visibility === 'public' ? 'Public' : document.visibility === 'team' ? 'Team' : 'Private';

  const renderPDFPreview = () => (
    <div className="flex flex-col items-center justify-center bg-gray-900 min-h-96">
      {document.url ? (
        <>
          <PDFDocument
            file={document.url}
            onLoadSuccess={({ numPages }) => setPdfNumPages(numPages)}
            loading={<div className="text-gray-400">Loading PDF...</div>}
          >
            <Page pageNumber={pdfPage} scale={zoom} />
          </PDFDocument>
          <div className="flex items-center space-x-2 mt-2">
            <button onClick={handlePdfZoomOut} className="p-2 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded" title="Zoom Out"><FiZoomOut size={16} /></button>
            <span className="text-sm text-gray-400 min-w-12 text-center">{Math.round(zoom * 100)}%</span>
            <button onClick={handlePdfZoomIn} className="p-2 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded" title="Zoom In"><FiZoomIn size={16} /></button>
            <button onClick={() => handlePdfPageChange(-1)} disabled={pdfPage <= 1} className="p-2 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded" title="Previous Page">&lt;</button>
            <span className="text-sm text-gray-400">Page {pdfPage} / {pdfNumPages}</span>
            <button onClick={() => handlePdfPageChange(1)} disabled={pdfPage >= pdfNumPages} className="p-2 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded" title="Next Page">&gt;</button>
          </div>
        </>
      ) : (
        <div className="text-center text-gray-400">
          <FiFile className="mx-auto h-12 w-12 mb-4" />
          <p>PDF preview not available</p>
          <button
            onClick={handleDownload}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Download PDF
          </button>
        </div>
      )}
    </div>
  );

  const renderTextPreview = () => (
    <div className="bg-gray-900 p-6 min-h-96 overflow-auto">
      <pre className="text-gray-200 font-mono text-sm whitespace-pre-wrap">
        {document.previewData?.content || document.contentText || 'No content available'}
      </pre>
    </div>
  );

  const renderCodePreview = () => (
    <div className="bg-gray-900 p-6 min-h-96 overflow-auto">
      <div className="flex items-center justify-between mb-4">
        <span className="text-gray-400 text-sm">
          Language: {document.previewData?.codeLanguage || 'Unknown'}
        </span>
      </div>
      <pre className="text-gray-200 font-mono text-sm overflow-x-auto">
        <code>{document.previewData?.content || document.contentText || 'No code content available'}</code>
      </pre>
    </div>
  );

  const renderSpreadsheetPreview = () => {
    const sheets = document.previewData?.spreadsheetData?.sheets || [];
    const currentSheet = sheets[activeSheet];

    return (
      <div className="bg-gray-900 min-h-96">
        {/* Sheet Tabs */}
        {sheets.length > 1 && (
          <div className="flex border-b border-gray-700 bg-gray-800">
            {sheets.map((sheet, index) => (
              <button
                key={index}
                onClick={() => setActiveSheet(index)}
                className={`px-4 py-2 text-sm font-medium ${
                  activeSheet === index
                    ? 'bg-gray-900 text-gray-200 border-b-2 border-blue-500'
                    : 'text-gray-400 hover:text-gray-200'
                }`}
              >
                {sheet.name}
              </button>
            ))}
          </div>
        )}

        {/* Spreadsheet Content */}
        <div className="overflow-auto max-h-96">
          {currentSheet?.data && currentSheet.data.length > 0 ? (
            <table className="w-full border-collapse">
              <tbody>
                {currentSheet.data.map((row, rowIndex) => (
                  <tr key={rowIndex}>
                    {row.map((cell, cellIndex) => (
                      <td
                        key={cellIndex}
                        className="border border-gray-700 px-3 py-2 text-sm text-gray-200 min-w-24"
                      >
                        {cell}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="text-center text-gray-400 py-8">
              <FiBarChart2 className="mx-auto h-12 w-12 mb-4" />
              <p>No spreadsheet data available</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderPreview = () => {
    if (document.processingStatus === 'processing') {
      return (
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Processing document...</p>
          </div>
        </div>
      );
    }

    if (document.processingStatus === 'failed') {
      return (
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <p className="text-red-400 mb-2">Failed to process document</p>
            <p className="text-gray-400 text-sm">{document.processingError}</p>
            <button
              onClick={handleDownload}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Download Original
            </button>
          </div>
        </div>
      );
    }

    switch (document.type) {
      case 'image':
        return renderImagePreview();
      case 'pdf':
        return renderPDFPreview();
      case 'document':
      case 'presentation':
        return renderTextPreview();
      case 'code':
        return renderCodePreview();
      case 'spreadsheet':
        return renderSpreadsheetPreview();
      default:
        return (
          <div className="flex items-center justify-center min-h-96">
            <div className="text-center">
              <div className="text-gray-400 mb-4">{getFileIcon(document.type)}</div>
              <p className="text-gray-400">Preview not available for this file type</p>
              <button
                onClick={handleDownload}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Download to view
              </button>
            </div>
          </div>
        );
    }
  };

  // Access change handlers
  const handleVisibilityChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newVisibility = e.target.value as 'private' | 'team' | 'public';
    setVisibility(newVisibility);
    if (onUpdateAccess) {
      onUpdateAccess(document.id, { sharedWith, visibility: newVisibility });
    }
  };
  const handleSharedWithChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selected = Array.from(e.target.selectedOptions).map(opt => opt.value);
    setSharedWith(selected);
    if (onUpdateAccess) {
      onUpdateAccess(document.id, { sharedWith: selected, visibility });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
      <div className={`bg-gray-800 rounded-lg shadow-2xl ${isFullscreen ? 'w-full h-full m-0' : 'w-11/12 h-5/6 max-w-6xl'}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            {getFileIcon(document.type)}
            <div>
              <h3 className="text-lg font-semibold text-gray-200">{document.name}</h3>
              <p className="text-sm text-gray-400">
                {(document.size / 1024 / 1024).toFixed(2)} MB • {document.type.toUpperCase()}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* OCR Text Indicator */}
            {document.ocrText && (
              <span className="px-2 py-1 bg-green-900 text-green-300 text-xs rounded-full">
                OCR Available
              </span>
            )}

            {/* Zoom Controls (images only) */}
            {document.type === 'image' && (
              <div className="flex items-center space-x-1">
                <button
                  onClick={handleZoomOut}
                  className="p-2 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded"
                  title="Zoom Out"
                >
                  <FiZoomOut size={16} />
                </button>
                <span className="text-sm text-gray-400 min-w-12 text-center">
                  {Math.round(zoom * 100)}%
                </span>
                <button
                  onClick={handleZoomIn}
                  className="p-2 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded"
                  title="Zoom In"
                >
                  <FiZoomIn size={16} />
                </button>
              </div>
            )}

            {/* Rotation Control (images only) */}
            {document.type === 'image' && (
              <button
                onClick={handleRotate}
                className="p-2 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded"
                title="Rotate"
              >
                <FiRotateCw size={16} />
              </button>
            )}

            {/* Reset View (images only) */}
            {document.type === 'image' && (
              <button
                onClick={handleReset}
                className="p-2 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded"
                title="Reset View"
              >
                Reset
              </button>
            )}

            {/* Fullscreen Toggle */}
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded"
              title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
            >
              {isFullscreen ? <FiMinimize2 size={16} /> : <FiMaximize2 size={16} />}
            </button>

            {/* Download Button */}
            <button
              onClick={handleDownload}
              className="p-2 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded"
              title="Download"
            >
              <FiDownload size={16} />
            </button>

            {/* Delete Button */}
            {onDelete && (
              <button
                onClick={() => onDelete(document)}
                className="p-2 text-red-400 hover:text-red-200 hover:bg-gray-700 rounded"
                title="Delete Document"
              >
                <FiTrash2 size={16} />
              </button>
            )}

            {/* Close Button */}
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded"
              title="Close"
            >
              <FiX size={16} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {renderPreview()}
        </div>

        {/* Tag Editor and Access */}
        <div className="p-4 border-t border-gray-700 bg-gray-900 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <div className="flex flex-wrap gap-2 mb-2">
              {tags.map(tag => (
                <span key={tag} className="bg-blue-800 text-blue-100 px-2 py-1 rounded-full text-xs flex items-center">
                  {tag}
                  {onUpdateTags && (
                    <button onClick={() => handleRemoveTag(tag)} className="ml-1 text-xs text-red-300 hover:text-red-500">&times;</button>
                  )}
                </span>
              ))}
            </div>
            {onUpdateTags && (
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={e => setTagInput(e.target.value)}
                  onKeyDown={e => { if (e.key === 'Enter') handleAddTag(); }}
                  className="px-2 py-1 rounded bg-gray-800 text-gray-100 border border-gray-600 text-xs"
                  placeholder="Add tag"
                />
                <button onClick={handleAddTag} className="px-2 py-1 bg-blue-700 text-white rounded text-xs">Add</button>
              </div>
            )}
          </div>
          {/* User Access List and Controls */}
          <div className="flex flex-col items-start min-w-[200px]">
            <span className="text-xs text-gray-400 mb-1">Access:</span>
            <div className="flex items-center gap-2 mb-2">
              <select
                value={visibility}
                onChange={handleVisibilityChange}
                className="px-2 py-1 rounded bg-gray-800 text-gray-100 border border-gray-600 text-xs"
              >
                <option value="private">Private</option>
                <option value="team">Team</option>
                <option value="public">Public</option>
              </select>
              {visibility !== 'public' && (
                <select
                  multiple
                  value={sharedWith}
                  onChange={handleSharedWithChange}
                  className="px-2 py-1 rounded bg-gray-800 text-gray-100 border border-gray-600 text-xs min-w-[120px]"
                  style={{ height: '2.5em' }}
                >
                  {users.map(user => (
                    <option key={user.id} value={user.id}>
                      {user.name}
                    </option>
                  ))}
                </select>
              )}
            </div>
            {/* Show avatars/names for selected users */}
            {visibility !== 'public' && sharedWith.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {users.filter(u => sharedWith.includes(u.id)).map(user => (
                  <span key={user.id} className="flex items-center gap-1 bg-gray-700 text-gray-200 px-2 py-1 rounded-full text-xs">
                    {user.avatar && <img src={user.avatar} alt={user.name} className="w-4 h-4 rounded-full" />}
                    {user.name}
                  </span>
                ))}
              </div>
            )}
            {visibility === 'public' && (
              <span className="text-xs text-gray-300">Anyone with the link can view</span>
            )}
          </div>
        </div>

        {/* Footer with OCR Text */}
        {document.ocrText && (
          <div className="p-4 border-t border-gray-700 bg-gray-900">
            <h4 className="text-sm font-medium text-gray-300 mb-2">Extracted Text (OCR)</h4>
            <div className="max-h-32 overflow-y-auto">
              <p className="text-sm text-gray-400 leading-relaxed">
                {document.ocrText}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentPreview; 