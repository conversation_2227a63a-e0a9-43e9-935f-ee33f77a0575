import React, { useRef, useState } from 'react';

interface FileUploaderProps {
  onUpload?: (files: File[], tags: string[]) => void;
}

const FileUploader: React.FC<FileUploaderProps> = ({ onUpload }) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setSelectedFiles(Array.from(e.target.files));
    }
  };

  const handleTagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === 'Enter' || e.key === ',') && tagInput.trim()) {
      e.preventDefault();
      if (!tags.includes(tagInput.trim())) {
        setTags([...tags, tagInput.trim()]);
      }
      setTagInput('');
    }
  };

  const handleTagDelete = (tagToDelete: string) => {
    setTags(tags.filter((tag) => tag !== tagToDelete));
  };

  const handleUpload = () => {
    if (onUpload && selectedFiles.length > 0) {
      onUpload(selectedFiles, tags);
    }
    setSelectedFiles([]);
    setTags([]);
  };

  return (
    <div className="border border-gray-300 dark:border-gray-700 rounded p-4 mb-4 bg-white dark:bg-gray-800">
      <h3 className="text-lg font-semibold mb-2">Upload Object</h3>
      <div
        className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded p-4 mb-2 text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          type="file"
          multiple
          hidden
          ref={fileInputRef}
          onChange={handleFileChange}
        />
        {selectedFiles.length === 0 ? (
          <span className="text-gray-400">Click or drag files here to upload</span>
        ) : (
          <ul className="text-left">
            {selectedFiles.map((file) => (
              <li key={file.name} className="text-gray-700 dark:text-gray-200">{file.name}</li>
            ))}
          </ul>
        )}
      </div>
      <input
        className="w-full border dark:border-gray-700 rounded px-3 py-2 mb-2 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"
        type="text"
        placeholder="Add tags (press Enter or comma)"
        value={tagInput}
        onChange={(e) => setTagInput(e.target.value)}
        onKeyDown={handleTagKeyDown}
      />
      <div className="flex flex-wrap gap-2 mb-2">
        {tags.map((tag) => (
          <span key={tag} className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200 px-2 py-1 rounded-full text-sm flex items-center">
            {tag}
            <button
              type="button"
              className="ml-1 text-blue-500 dark:text-blue-300 hover:text-blue-700 dark:hover:text-blue-400"
              onClick={() => handleTagDelete(tag)}
            >
              &times;
            </button>
          </span>
        ))}
      </div>
      <button
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50"
        disabled={selectedFiles.length === 0}
        onClick={handleUpload}
      >
        Upload
      </button>
    </div>
  );
};

export default FileUploader; 