import React from 'react';
// @ts-ignore
import ForceGraph2D from 'react-force-graph-2d';

interface UploadedObject {
  id: string;
  name: string;
  type: string;
  tags: string[];
}

interface GraphViewModalProps {
  open: boolean;
  onClose: () => void;
  tag: string;
  objects: UploadedObject[];
}

const GraphViewModal: React.FC<GraphViewModalProps> = ({ open, onClose, tag, objects }) => {
  if (!open) return null;
  // Build graph data
  const nodes = [
    { id: tag, type: 'tag', name: tag },
    ...objects.map((obj) => ({ id: obj.id, type: 'object', name: obj.name })),
  ];
  const links = objects.map((obj) => ({ source: tag, target: obj.id }));
  const graphData = { nodes, links };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg w-full max-w-3xl p-4 relative">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-800 dark:hover:text-gray-200"
          aria-label="Close"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-100">Graph View: {tag}</h2>
        <div style={{ height: 500 }}>
          <ForceGraph2D
            graphData={graphData}
            nodeLabel={(node: any) => node.name}
            nodeAutoColorBy="type"
          />
        </div>
      </div>
    </div>
  );
};

export default GraphViewModal; 