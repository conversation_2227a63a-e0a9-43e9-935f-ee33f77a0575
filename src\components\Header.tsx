import React from 'react';

const toggleDarkMode = () => {
  const html = document.documentElement;
  if (html.classList.contains('dark')) {
    html.classList.remove('dark');
  } else {
    html.classList.add('dark');
  }
};

const Header: React.FC = () => (
  <header className="fixed top-0 left-0 right-0 h-16 bg-blue-600 dark:bg-gray-800 flex items-center justify-between px-6 z-10 shadow text-white dark:text-gray-100">
    <h1 className="text-xl font-bold">Task Manager</h1>
    <div className="flex items-center gap-4">
      <button
        onClick={toggleDarkMode}
        className="w-8 h-8 flex items-center justify-center rounded-full bg-white bg-opacity-20 hover:bg-opacity-40 transition"
        title="Toggle dark mode"
      >
        {/* Sun/Moon icon */}
        <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M12 3v1m0 16v1m8.66-13.66l-.71.71M4.05 19.95l-.71.71M21 12h-1M4 12H3m16.66 5.66l-.71-.71M4.05 4.05l-.71-.71M16 12a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
      </button>
      {/* User icon placeholder */}
      <div className="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5.121 17.804A9 9 0 1112 21a8.963 8.963 0 01-6.879-3.196z" /><path strokeLinecap="round" strokeLinejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
      </div>
    </div>
  </header>
);

export default Header; 