import React, { useState } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
} from '@dnd-kit/sortable';
import { FiPlus, FiMoreVertical } from 'react-icons/fi';
import TaskCard from './TaskCard';
import type { Task, Project } from '../App';

interface KanbanColumn {
  id: string;
  name: string;
  color: string;
  status: string;
  tasks: Task[];
}

interface KanbanBoardProps {
  tasks: Task[];
  project?: Project;
  onTaskClick?: (task: Task) => void;
  onToggleComplete?: (task: Task, completed: boolean) => void;
  onUpdateTaskStatus?: (taskId: string, newStatus: string) => void;
  onAddTask?: (status: string) => void;
}

const DEFAULT_COLUMNS: Kanban<PERSON>olumn[] = [
  {
    id: 'backlog',
    name: 'Backlog',
    color: '#6B7280',
    status: 'backlog',
    tasks: []
  },
  {
    id: 'todo',
    name: 'To Do',
    color: '#3B82F6',
    status: 'todo',
    tasks: []
  },
  {
    id: 'in-progress',
    name: 'In Progress',
    color: '#F59E0B',
    status: 'in-progress',
    tasks: []
  },
  {
    id: 'review',
    name: 'Review',
    color: '#8B5CF6',
    status: 'review',
    tasks: []
  },
  {
    id: 'done',
    name: 'Done',
    color: '#10B981',
    status: 'done',
    tasks: []
  }
];

const KanbanBoard: React.FC<KanbanBoardProps> = ({
  tasks,
  project,
  onTaskClick,
  onToggleComplete,
  onUpdateTaskStatus,
  onAddTask
}) => {
  const [columns, setColumns] = useState<KanbanColumn[]>(() => {
    // Initialize columns with project-specific config or defaults
    const projectColumns = project?.kanbanConfig?.columns || DEFAULT_COLUMNS;
    
    // Group tasks by status
    const groupedTasks = projectColumns.map(column => ({
      ...column,
      tasks: tasks.filter(task => task.status === column.status || 
        (!task.status && column.status === 'todo' && !task.completed) ||
        (task.completed && column.status === 'done'))
    }));
    
    return groupedTasks;
  });

  const [activeTask, setActiveTask] = useState<Task | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const taskId = active.id as string;
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      setActiveTask(task);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveTask(null);

    if (!over) return;

    const taskId = active.id as string;
    const columnId = over.id as string;

    const sourceColumn = columns.find(col => 
      col.tasks.some(task => task.id === taskId)
    );
    const destColumn = columns.find(col => col.id === columnId);

    if (!sourceColumn || !destColumn || sourceColumn.id === destColumn.id) return;

    // Update task status
    if (onUpdateTaskStatus) {
      onUpdateTaskStatus(taskId, destColumn.status);
    }

    // Update columns state
    setColumns(prevColumns => prevColumns.map(col => {
      if (col.id === sourceColumn.id) {
        return {
          ...col,
          tasks: col.tasks.filter(task => task.id !== taskId)
        };
      }
      if (col.id === destColumn.id) {
        const task = sourceColumn.tasks.find(t => t.id === taskId);
        if (task) {
          return {
            ...col,
            tasks: [...col.tasks, { 
              ...task, 
              status: destColumn.status as 'todo' | 'in-progress' | 'review' | 'done' | 'backlog'
            }]
          };
        }
      }
      return col;
    }));
  };

  return (
    <div className="flex-1 overflow-x-auto">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex gap-4 p-4 min-h-full">
          {columns.map(column => (
            <div key={column.id} className="flex-shrink-0 w-80">
              <div className="bg-[#1e222a] rounded-lg p-4 h-full">
                {/* Column Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: column.color }}
                    />
                    <h3 className="font-semibold text-gray-200">{column.name}</h3>
                    <span className="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">
                      {column.tasks.length}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => onAddTask?.(column.status)}
                      className="text-gray-400 hover:text-gray-200 p-1 rounded hover:bg-gray-700"
                      title="Add task"
                    >
                      <FiPlus size={16} />
                    </button>
                    <button className="text-gray-400 hover:text-gray-200 p-1 rounded hover:bg-gray-700">
                      <FiMoreVertical size={16} />
                    </button>
                  </div>
                </div>

                {/* Column Content */}
                <div className="min-h-[200px]">
                  <div className="space-y-2">
                    {column.tasks.map((task) => (
                      <div key={task.id} className="cursor-grab active:cursor-grabbing">
                        <TaskCard
                          task={task}
                          project={project}
                          onClick={() => onTaskClick?.(task)}
                          onToggleComplete={completed => onToggleComplete?.(task, completed)}
                        />
                      </div>
                    ))}
                  </div>
                  
                  {/* Empty state */}
                  {column.tasks.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <div className="text-sm">No tasks</div>
                      <button
                        onClick={() => onAddTask?.(column.status)}
                        className="text-blue-400 hover:text-blue-300 text-sm mt-2"
                      >
                        Add task
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        <DragOverlay>
          {activeTask ? (
            <div className="opacity-80">
              <TaskCard
                task={activeTask}
                project={project}
                onClick={() => onTaskClick?.(activeTask)}
                onToggleComplete={completed => onToggleComplete?.(activeTask, completed)}
              />
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
};

export default KanbanBoard; 