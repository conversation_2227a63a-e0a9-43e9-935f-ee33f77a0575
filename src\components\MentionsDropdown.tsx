import React, { useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import UserAvatar from './UserAvatar';
import type { MentionSuggestion } from '../utils/useMentions';

interface MentionsDropdownProps {
  suggestions: MentionSuggestion[];
  selectedIndex: number;
  onSuggestionClick: (suggestion: MentionSuggestion) => void;
  onSuggestionHover: (index: number) => void;
  visible: boolean;
  position?: { x: number; y: number };
  textareaRef: React.RefObject<HTMLTextAreaElement>;
  dropdownRef?: React.RefObject<HTMLDivElement | null>;
}

const MentionsDropdown: React.FC<MentionsDropdownProps> = ({
  suggestions,
  selectedIndex,
  onSuggestionClick,
  onSuggestionHover,
  visible,
  position,
  textareaRef,
  dropdownRef
}) => {
  const localDropdownRef = useRef<HTMLDivElement>(null);
  const ref = dropdownRef || localDropdownRef;
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });

  // Calculate dropdown position based on textarea cursor position
  useEffect(() => {
    if (!visible || !textareaRef.current || !ref.current) return;

    const textarea = textareaRef.current;
    const dropdown = ref.current;
    
    // Get textarea position and dimensions
    const textareaRect = textarea.getBoundingClientRect();
    
    // Create a temporary span to measure text dimensions
    const span = document.createElement('span');
    span.style.position = 'absolute';
    span.style.visibility = 'hidden';
    span.style.whiteSpace = 'pre-wrap';
    span.style.wordWrap = 'break-word';
    span.style.font = window.getComputedStyle(textarea).font;
    span.style.width = `${textarea.offsetWidth}px`;
    span.style.padding = window.getComputedStyle(textarea).padding;
    
    // Get text before cursor
    const textBeforeCursor = textarea.value.substring(0, textarea.selectionStart || 0);
    span.textContent = textBeforeCursor;
    document.body.appendChild(span);
    
    // Calculate cursor position
    const lines = textBeforeCursor.split('\n');
    const currentLine = lines[lines.length - 1];
    const lineHeight = parseInt(window.getComputedStyle(textarea).lineHeight);
    
    // Create another span to measure the current line
    const lineSpan = document.createElement('span');
    lineSpan.style.position = 'absolute';
    lineSpan.style.visibility = 'hidden';
    lineSpan.style.font = window.getComputedStyle(textarea).font;
    lineSpan.textContent = currentLine;
    document.body.appendChild(lineSpan);
    
    const currentLineWidth = lineSpan.offsetWidth;
    const currentLineNumber = lines.length - 1;
    
    // Clean up temporary elements
    document.body.removeChild(span);
    document.body.removeChild(lineSpan);
    
    // Calculate position
    const top = textareaRect.top + (currentLineNumber * lineHeight) + lineHeight;
    const left = textareaRect.left + Math.min(currentLineWidth, textarea.offsetWidth - 20);
    
    // Ensure dropdown doesn't go off-screen
    const dropdownWidth = 280; // Approximate dropdown width
    const dropdownHeight = Math.min(suggestions.length * 48, 240); // 48px per item, max 5 items visible
    
    const adjustedLeft = Math.max(0, Math.min(left, window.innerWidth - dropdownWidth - 10));
    const adjustedTop = top + dropdownHeight > window.innerHeight 
      ? textareaRect.top - dropdownHeight - 10 
      : top;
    
    setDropdownPosition({
      top: adjustedTop,
      left: adjustedLeft
    });
  }, [visible, suggestions.length, textareaRef, ref]);

  if (!visible || suggestions.length === 0) {
    return null;
  }

  const dropdown = (
    <div
      ref={ref}
      className="fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl max-h-60 overflow-y-auto"
      style={{
        top: dropdownPosition.top,
        left: dropdownPosition.left,
        minWidth: '280px',
        pointerEvents: 'auto',
        zIndex: 9999
      }}
    >
      <div className="p-2">
        <div className="text-xs text-gray-400 px-3 py-1 border-b border-gray-700">
          Mention a team member
        </div>
        {suggestions.map((suggestion, index) => (
          <button
            key={suggestion.id}
            className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-md transition-colors ${
              index === selectedIndex
                ? 'bg-blue-600 text-white'
                : 'text-gray-300 hover:bg-gray-700'
            }`}
            onClick={() => {
              console.log('Dropdown click:', suggestion);
              onSuggestionClick(suggestion);
            }}
            onMouseEnter={() => onSuggestionHover(index)}
            type="button"
          >
            <UserAvatar 
              user={{
                id: suggestion.id,
                name: suggestion.name,
                email: suggestion.email,
                role: suggestion.role as any,
                isOnline: false,
                lastSeen: ''
              }} 
              size="sm" 
            />
            <div className="flex-1 min-w-0">
              <div className="font-medium truncate">{suggestion.name}</div>
              <div className={`text-xs truncate ${
                index === selectedIndex ? 'text-blue-100' : 'text-gray-400'
              }`}>
                {suggestion.email}
              </div>
            </div>
            <div className={`text-xs px-2 py-1 rounded ${
              index === selectedIndex 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-600 text-gray-300'
            }`}>
              {suggestion.role}
            </div>
          </button>
        ))}
      </div>
    </div>
  );

  return ReactDOM.createPortal(dropdown, document.body);
};

export default MentionsDropdown; 