import React, { useRef, forwardRef, useEffect } from 'react';
import { FiAtSign } from 'react-icons/fi';
import MentionsDropdown from './MentionsDropdown';
import { useMentions } from '../utils/useMentions';
import type { User } from '../App';

interface MentionsInputProps {
  users: User[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  className?: string;
  disabled?: boolean;
  onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  showMentionsHint?: boolean;
  getMentionedUsers?: () => string[];
}

const MentionsInput = forwardRef<HTMLTextAreaElement, MentionsInputProps>(
  (
    {
      users,
      value,
      onChange,
      placeholder = "Type @ to mention team members...",
      rows = 3,
      className = "",
      disabled = false,
      onKeyDown,
      onFocus,
      onBlur,
      showMentionsHint = true,
      getMentionedUsers
    },
    ref
  ) => {
    const textareaRef = useRef<HTMLTextAreaElement | null>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const inputRef = ref || textareaRef;

    const {
      suggestions,
      showSuggestions,
      setShowSuggestions,
      selectedIndex,
      mentionPosition,
      handleInputChange,
      handleKeyDown: handleMentionsKeyDown,
      handleSuggestionClick,
      handleSuggestionHover,
      insertMention,
      getMentionedUsers: getMentionsFromHook,
      closeDropdown
    } = useMentions(users, value, textareaRef);

    // Debug logging
    console.log('MentionsInput users:', users);
    console.log('MentionsInput suggestions:', suggestions);

    // Outside click handler
    useEffect(() => {
      if (!showSuggestions) return;
      function handleClickOutside(event: MouseEvent) {
        const target = event.target as Node;
        if (
          textareaRef.current &&
          !textareaRef.current.contains(target) &&
          dropdownRef.current &&
          !dropdownRef.current.contains(target)
        ) {
          closeDropdown();
        }
      }
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [showSuggestions, closeDropdown]);

    // Handle input change
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      handleInputChange(e);
      onChange(e.target.value);
    };

    // Handle key down
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      handleMentionsKeyDown(e);
      onKeyDown?.(e);
    };

    // Handle suggestion click
    const handleDropdownSuggestionClick = (suggestion: any) => {
      const newValue = insertMention(suggestion);
      if (typeof newValue === 'string') {
        onChange(newValue);
      }
    };

    return (
      <div className="relative">
        <textarea
          ref={inputRef as React.RefObject<HTMLTextAreaElement>}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onFocus={onFocus}
          onBlur={onBlur}
          placeholder={placeholder}
          rows={rows}
          disabled={disabled}
          className={`w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-sm text-gray-300 placeholder-gray-400 resize-none focus:outline-none focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${className}`}
        />
        
        {showMentionsHint && (
          <div className="flex items-center gap-1 text-xs text-gray-400 mt-2">
            <FiAtSign className="w-3 h-3" />
            <span>Use @ to mention team members</span>
          </div>
        )}

        <MentionsDropdown
          suggestions={suggestions}
          selectedIndex={selectedIndex}
          onSuggestionClick={handleDropdownSuggestionClick}
          onSuggestionHover={handleSuggestionHover}
          visible={showSuggestions}
          textareaRef={inputRef as React.RefObject<HTMLTextAreaElement>}
          dropdownRef={dropdownRef}
        />
      </div>
    );
  }
);

MentionsInput.displayName = 'MentionsInput';

export default MentionsInput; 