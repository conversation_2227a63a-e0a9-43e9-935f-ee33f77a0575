import React, { useState } from 'react';

const MentionsTest: React.FC = () => {
  const [value, setValue] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setValue(e.target.value);
  };

  return (
    <div className="p-8 bg-gray-900 min-h-screen">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Mentions Test (react-mentions removed)</h1>
        <div className="space-y-4">
          <label className="block text-gray-300 mb-2">Test Input:</label>
          <textarea
            value={value}
            onChange={handleChange}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-sm text-gray-300 placeholder-gray-400 resize-none focus:outline-none focus:border-blue-500"
            placeholder="Type @ to mention users..."
            rows={3}
          />
        </div>
      </div>
    </div>
  );
};

export default MentionsTest; 