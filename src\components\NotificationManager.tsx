import React, { useEffect, useRef } from 'react';
import type { Task, Reminder } from '../App';

type NotificationSettings = {
  enabled: boolean;
  permission: NotificationPermission;
  dueDateReminders: boolean;
  customReminders: boolean;
  soundEnabled: boolean;
  desktopNotifications: boolean;
};

type NotificationManagerProps = {
  tasks: Task[];
  notificationSettings: NotificationSettings;
  onUpdateReminder: (taskId: string, reminderId: string, updates: Partial<Reminder>) => void;
};

const NotificationManager: React.FC<NotificationManagerProps> = ({
  tasks,
  notificationSettings,
  onUpdateReminder
}) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Check for due reminders
  const checkReminders = () => {
    if (!notificationSettings.enabled || notificationSettings.permission !== 'granted') {
      return;
    }

    const now = new Date();
    const activeTasks = tasks.filter(task => !task.completed);

    activeTasks.forEach(task => {
      const taskReminders = task.reminders || [];
      
      taskReminders.forEach(reminder => {
        if (!reminder.isActive) return;

        const reminderTime = new Date(reminder.time);
        const timeDiff = reminderTime.getTime() - now.getTime();
        
        // Show notification if reminder is due (within 1 minute)
        if (timeDiff <= 60000 && timeDiff > -60000) {
          showNotification(task, reminder);
          
          // Mark reminder as inactive to prevent duplicate notifications
          onUpdateReminder(task.id, reminder.id, { isActive: false });
        }
      });
    });
  };

  // Show browser notification
  const showNotification = (task: Task, reminder: Reminder) => {
    if (!notificationSettings.desktopNotifications || !('Notification' in window)) {
      return;
    }

    const notification = new Notification('Task Reminder', {
      body: reminder.message || `${task.name} is due soon`,
      icon: '/favicon.ico',
      tag: `reminder-${reminder.id}`,
      requireInteraction: true
    });

    // Handle notification click
    notification.onclick = () => {
      window.focus();
      // You could navigate to the task detail here
    };

    // Play sound if enabled
    if (notificationSettings.soundEnabled) {
      playNotificationSound();
    }
  };

  // Play notification sound
  const playNotificationSound = () => {
    try {
      const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
      audio.volume = 0.3;
      audio.play().catch(() => {
        // Ignore errors if audio fails to play
      });
    } catch (error) {
      // Ignore errors if audio is not supported
    }
  };

  // Set up interval to check reminders
  useEffect(() => {
    if (notificationSettings.enabled && notificationSettings.permission === 'granted') {
      // Check every minute
      intervalRef.current = setInterval(checkReminders, 60000);
      
      // Initial check
      checkReminders();
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [tasks, notificationSettings]);

  // Request notification permission if needed
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      // You could show a permission request UI here
      console.log('Notification permission not granted');
    }
  }, []);

  return null; // This component doesn't render anything
};

export default NotificationManager; 