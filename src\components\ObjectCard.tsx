import React from 'react';

interface UploadedObject {
  id: string;
  name: string;
  type: string;
  tags: string[];
}

interface ObjectCardProps {
  object: UploadedObject;
}

const ObjectCard: React.FC<ObjectCardProps> = ({ object }) => (
  <div className="border rounded-lg p-4 bg-white dark:bg-gray-800 shadow-sm flex flex-col gap-2">
    <div className="font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
      <span>{object.name}</span>
      <span className="text-xs text-gray-400 dark:text-gray-300">({object.type || 'file'})</span>
    </div>
    <div className="flex flex-wrap gap-2">
      {object.tags.map((tag) => (
        <span key={tag} className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200 px-2 py-1 rounded-full text-xs">{tag}</span>
      ))}
    </div>
  </div>
);

export default ObjectCard; 