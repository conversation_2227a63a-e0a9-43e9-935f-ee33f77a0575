import React from 'react';
import ObjectCard from './ObjectCard';

interface UploadedObject {
  id: string;
  name: string;
  type: string;
  tags: string[];
}

interface ObjectGalleryProps {
  objects: UploadedObject[];
}

const ObjectGallery: React.FC<ObjectGalleryProps> = ({ objects }) => (
  <div>
    {objects.length === 0 ? (
      <div className="text-gray-400 text-center py-8">No objects uploaded yet.</div>
    ) : (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {objects.map((obj) => <ObjectCard key={obj.id} object={obj} />)}
      </div>
    )}
  </div>
);

export default ObjectGallery; 