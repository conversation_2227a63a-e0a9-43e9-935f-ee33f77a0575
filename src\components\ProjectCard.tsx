import React from 'react';
import { FiStar, FiMoreVertical, FiFolder } from 'react-icons/fi';
import type { Project } from '../App';

interface ProjectCardProps {
  project: Project;
  onToggleFavorite: (projectId: string) => void;
  onEdit: (project: Project) => void;
  onDelete: (projectId: string) => void;
  onClick?: () => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ 
  project, 
  onToggleFavorite, 
  onEdit, 
  onDelete, 
  onClick 
}) => {
  const progressPercentage = project.taskCount > 0 
    ? Math.round((project.completedTaskCount / project.taskCount) * 100) 
    : 0;

  return (
    <div 
      className="bg-[#23272f] rounded-xl p-6 border border-[#23272f] hover:border-gray-600 transition-all duration-200 cursor-pointer group"
      onClick={onClick}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div 
            className="w-10 h-10 rounded-lg flex items-center justify-center"
            style={{ backgroundColor: project.color }}
          >
            <FiFolder className="text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-100 group-hover:text-white transition-colors">
              {project.name}
            </h3>
            <p className="text-sm text-gray-400 mt-1">{project.description}</p>
          </div>
        </div>
        <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleFavorite(project.id);
            }}
            className={`p-1 rounded hover:bg-gray-700 transition-colors ${
              project.isFavorite ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'
            }`}
          >
            <FiStar className={project.isFavorite ? 'fill-current' : ''} />
          </button>
          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation();
                // Toggle dropdown menu
              }}
              className="p-1 rounded hover:bg-gray-700 transition-colors text-gray-400 hover:text-gray-200"
            >
              <FiMoreVertical />
            </button>
            {/* Dropdown menu would go here */}
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-400">Progress</span>
          <span className="text-gray-200 font-medium">
            {project.completedTaskCount}/{project.taskCount} tasks
          </span>
        </div>
        
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div 
            className="h-2 rounded-full transition-all duration-300"
            style={{ 
              width: `${progressPercentage}%`,
              backgroundColor: project.color 
            }}
          />
        </div>
        
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>{progressPercentage}% complete</span>
          <span>Updated {new Date(project.updatedAt).toLocaleDateString()}</span>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard; 