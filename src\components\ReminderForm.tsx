import React, { useState, useEffect } from 'react';
import { FiBell, FiX, FiCalendar, FiClock } from 'react-icons/fi';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import type { Reminder } from '../App';

type ReminderFormProps = {
  onAddReminder: (reminder: Omit<Reminder, 'id' | 'taskId' | 'createdAt'>) => void;
  onCancel: () => void;
  defaultType?: 'due_date' | 'custom';
  defaultTime?: string;
};

const ReminderForm: React.FC<ReminderFormProps> = ({
  onAddReminder,
  onCancel,
  defaultType = 'custom',
  defaultTime
}) => {
  const [type, setType] = useState<'due_date' | 'custom'>(defaultType);
  const [time, setTime] = useState<Date | null>(defaultTime ? new Date(defaultTime) : null);
  const [message, setMessage] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!time) return;

    onAddReminder({
      type,
      time: time.toISOString(),
      message: message || (type === 'due_date' ? 'Task is due soon' : 'Custom reminder'),
      isActive: true
    });
  };

  const getDefaultMessage = () => {
    if (type === 'due_date') {
      return 'Task is due soon';
    }
    return 'Custom reminder';
  };

  useEffect(() => {
    function handleKeyDown(e: KeyboardEvent) {
      if (e.key === 'Escape') {
        onCancel();
      }
    }
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onCancel]);

  return (
    <div className="bg-gray-900 rounded-lg p-4 border border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-100 flex items-center">
          <FiBell className="mr-2" />
          Add Reminder
        </h3>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-gray-200"
          aria-label="Close"
        >
          <FiX />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Reminder Type */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Reminder Type
          </label>
          <div className="flex gap-2">
            <button
              type="button"
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                type === 'due_date'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              onClick={() => setType('due_date')}
            >
              Due Date
            </button>
            <button
              type="button"
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                type === 'custom'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              onClick={() => setType('custom')}
            >
              Custom Time
            </button>
          </div>
        </div>

        {/* Date/Time Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            {type === 'due_date' ? 'Remind me' : 'Reminder Time'}
          </label>
          <div className="relative">
            <DatePicker
              selected={time}
              onChange={(date) => setTime(date)}
              showTimeSelect
              timeIntervals={15}
              dateFormat="MMM dd, yyyy h:mm aa"
              placeholderText="Select date and time"
              className="w-full bg-gray-800 border border-gray-700 rounded-md px-3 py-2 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-blue-500"
              calendarClassName="bg-gray-900 text-gray-100 border border-gray-700 rounded shadow"
              popperPlacement="bottom"
              showPopperArrow={false}
              minDate={new Date()}
            />
          </div>
        </div>

        {/* Message */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Message (optional)
          </label>
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder={getDefaultMessage()}
            className="w-full bg-gray-800 border border-gray-700 rounded-md px-3 py-2 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-blue-500"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 px-4 py-2 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!time}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-700 disabled:text-gray-500 disabled:cursor-not-allowed transition-colors"
          >
            Add Reminder
          </button>
        </div>
      </form>
    </div>
  );
};

export default ReminderForm; 