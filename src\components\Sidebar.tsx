import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { FiPlus, FiMic, FiSearch, FiInbox, FiCalendar, FiChevronDown, FiUser, FiHelpCircle, FiStar, FiFolder, FiFile, FiMessageCircle } from 'react-icons/fi';
import type { Project } from '../App';

const DRAWER_WIDTH = 280;

type SidebarProps = {
  onAddTask?: () => void;
  projects?: Project[];
  onToggleProjectFavorite?: (projectId: string) => void;
  onChatClick: () => void;
};

const Sidebar: React.FC<SidebarProps> = ({ onAddTask, projects = [], onToggleProjectFavorite, onChatClick }) => {
  const [showAllProjects, setShowAllProjects] = useState(false);

  const favoriteProjects = projects.filter(p => p.isFavorite);
  const otherProjects = projects.filter(p => !p.isFavorite);
  const displayedProjects = showAllProjects ? otherProjects : otherProjects.slice(0, 3);

  return (
    <aside
      className="fixed top-0 left-0 h-screen bg-[#191c22] border-r border-gray-800 flex flex-col text-gray-200"
      style={{ width: DRAWER_WIDTH }}
    >
      {/* Profile section */}
      <div className="flex items-center gap-3 px-6 pt-6 pb-4">
        <div className="bg-orange-500 rounded-full w-8 h-8 flex items-center justify-center font-bold text-lg">M</div>
        <div className="flex-1">
          <div className="font-semibold leading-tight">etesone</div>
          <div className="text-xs text-gray-400">Pro</div>
        </div>
        <button className="text-gray-400 hover:text-gray-200"><span><FiUser size={20} /></span></button>
      </div>
      
      {/* Add Task button */}
      <div className="flex items-center justify-between px-6 pb-2">
        <button className="flex items-center text-red-400 hover:text-red-500 font-semibold text-base focus:outline-none" onClick={onAddTask}>
          <span className="bg-red-400 text-white rounded-full p-1 mr-2"><FiPlus size={20} /></span>
          Add task
        </button>
        <span className="text-red-400"><FiMic size={20} /></span>
      </div>
      
      {/* Search */}
      <NavLink to="/search" className="flex items-center px-6 py-2 text-gray-300 hover:bg-gray-800 transition-colors">
        <span className="mr-3"><FiSearch size={18} /></span>
        Search
      </NavLink>
      
      {/* Main nav */}
      <nav className="flex-1 mt-2">
        <ul className="space-y-1">
          <li>
            <NavLink to="/inbox" className={({ isActive }) =>
              `flex items-center px-6 py-2 rounded-r-full hover:bg-gray-800 transition-colors ${isActive ? 'bg-gray-800 font-semibold text-red-400' : 'text-gray-200'}`
            }>
              <span className="mr-3"><FiInbox size={18} /></span>
              Inbox
              <span className="ml-auto text-xs text-gray-400 font-semibold">42</span>
            </NavLink>
          </li>
          <li>
            <button
              className="flex items-center w-full px-4 py-2 text-left hover:bg-gray-700 transition-colors"
              onClick={onChatClick}
            >
              <FiMessageCircle className="w-5 h-5 mr-3" />
              <span>Chat</span>
            </button>
          </li>
          <li>
            <NavLink to="/today" className={({ isActive }) =>
              `flex items-center px-6 py-2 rounded-r-full hover:bg-gray-800 transition-colors ${isActive ? 'bg-gray-800 font-semibold text-red-400' : 'text-gray-200'}`
            }>
              <span className="mr-3"><FiCalendar size={18} /></span>
              Today
              <span className="ml-auto text-xs text-gray-400 font-semibold">36</span>
            </NavLink>
          </li>
          <li>
            <NavLink to="/upcoming" className={({ isActive }) =>
              `flex items-center px-6 py-2 rounded-r-full hover:bg-gray-800 transition-colors ${isActive ? 'bg-gray-800 font-semibold text-red-400' : 'text-gray-200'}`
            }>
              <span className="mr-3"><svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6l4 2" /></svg></span>
              Upcoming
            </NavLink>
          </li>
          <li>
            <NavLink to="/projects" className={({ isActive }) =>
              `flex items-center px-6 py-2 rounded-r-full hover:bg-gray-800 transition-colors ${isActive ? 'bg-gray-800 font-semibold text-red-400' : 'text-gray-200'}`
            }>
              <span className="mr-3"><FiFolder size={18} /></span>
              Projects
              <span className="ml-auto text-xs text-gray-400 font-semibold">{projects.length}</span>
            </NavLink>
          </li>
          <li>
            <NavLink to="/documents" className={({ isActive }) =>
              `flex items-center px-6 py-2 rounded-r-full hover:bg-gray-800 transition-colors ${isActive ? 'bg-gray-800 font-semibold text-red-400' : 'text-gray-200'}`
            }>
              <span className="mr-3"><FiFile size={18} /></span>
              Documents
            </NavLink>
          </li>
          <li>
            <NavLink to="/notes" className={({ isActive }) =>
              `flex items-center px-6 py-2 rounded-r-full hover:bg-gray-800 transition-colors ${isActive ? 'bg-gray-800 font-semibold text-red-400' : 'text-gray-200'}`
            }>
              <span className="mr-3"><FiFile size={18} /></span>
              Notes
            </NavLink>
          </li>
          <li>
            <NavLink to="/users" className={({ isActive }) =>
              `flex items-center px-6 py-2 rounded-r-full hover:bg-gray-800 transition-colors ${isActive ? 'bg-gray-800 font-semibold text-red-400' : 'text-gray-200'}`
            }>
              <span className="mr-3"><FiUser size={18} /></span>
              Users
            </NavLink>
          </li>
        </ul>
        
        {/* Favorites section */}
        {favoriteProjects.length > 0 && (
          <>
            <div className="mt-6 px-6 text-xs text-gray-400 font-bold tracking-wider">FAVORITES</div>
            <ul className="space-y-1 mt-2">
              {favoriteProjects.map(project => (
                <li key={project.id}>
                  <NavLink 
                    to={`/projects/${project.id}`} 
                    className={({ isActive }) =>
                      `flex items-center px-6 py-2 rounded-r-full hover:bg-gray-800 transition-colors ${isActive ? 'bg-gray-800 font-semibold text-red-400' : 'text-gray-200'}`
                    }
                  >
                    <div 
                      className="w-3 h-3 rounded mr-3 flex-shrink-0"
                      style={{ backgroundColor: project.color }}
                    />
                    <span className="truncate flex-1">{project.name}</span>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onToggleProjectFavorite?.(project.id);
                      }}
                      className="ml-2 text-yellow-400 hover:text-yellow-300"
                    >
                      <FiStar size={14} className="fill-current" />
                    </button>
                    <span className="ml-2 text-xs text-gray-400 font-semibold">
                      {project.taskCount}
                    </span>
                  </NavLink>
                </li>
              ))}
            </ul>
          </>
        )}
        
        {/* Projects section */}
        {otherProjects.length > 0 && (
          <>
            <div className="mt-6 px-6 text-xs text-gray-400 font-bold tracking-wider">PROJECTS</div>
            <ul className="space-y-1 mt-2">
              {displayedProjects.map(project => (
                <li key={project.id}>
                  <NavLink 
                    to={`/projects/${project.id}`} 
                    className={({ isActive }) =>
                      `flex items-center px-6 py-2 rounded-r-full hover:bg-gray-800 transition-colors ${isActive ? 'bg-gray-800 font-semibold text-red-400' : 'text-gray-200'}`
                    }
                  >
                    <div 
                      className="w-3 h-3 rounded mr-3 flex-shrink-0"
                      style={{ backgroundColor: project.color }}
                    />
                    <span className="truncate flex-1">{project.name}</span>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onToggleProjectFavorite?.(project.id);
                      }}
                      className="ml-2 text-gray-400 hover:text-yellow-400"
                    >
                      <FiStar size={14} />
                    </button>
                    <span className="ml-2 text-xs text-gray-400 font-semibold">
                      {project.taskCount}
                    </span>
                  </NavLink>
                </li>
              ))}
              
              {otherProjects.length > 3 && (
                <li>
                  <button
                    onClick={() => setShowAllProjects(!showAllProjects)}
                    className="flex items-center px-6 py-2 text-gray-400 hover:text-gray-200 text-sm w-full"
                  >
                    <span className="mr-3">
                      <FiChevronDown 
                        size={14} 
                        className={`transition-transform ${showAllProjects ? 'rotate-180' : ''}`}
                      />
                    </span>
                    {showAllProjects ? 'Show less' : `Show ${otherProjects.length - 3} more`}
                  </button>
                </li>
              )}
            </ul>
          </>
        )}
      </nav>
      
      {/* Help & resources at the bottom */}
      <div className="mt-auto mb-4 px-6">
        <a href="#" className="flex items-center text-gray-400 hover:text-gray-200 text-sm">
          <span className="mr-2"><FiHelpCircle size={18} /></span>
          Help & resources
        </a>
      </div>
    </aside>
  );
};

export default Sidebar; 