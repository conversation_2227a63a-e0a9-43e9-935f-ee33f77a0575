import React from 'react';

interface TagFilterBarProps {
  tags: string[];
  selectedTag: string | null;
  onTagSelect: (tag: string | null) => void;
}

const TagFilterBar: React.FC<TagFilterBarProps> = ({ tags, selectedTag, onTagSelect }) => (
  <div className="flex flex-wrap gap-2 mb-4">
    <button
      className={`px-3 py-1 rounded-full border ${selectedTag === null ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700'}`}
      onClick={() => onTagSelect(null)}
    >
      All
    </button>
    {tags.map((tag) => (
      <button
        key={tag}
        className={`px-3 py-1 rounded-full border ${selectedTag === tag ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700'}`}
        onClick={() => onTagSelect(tag)}
      >
        {tag}
      </button>
    ))}
  </div>
);

export default TagFilterBar; 