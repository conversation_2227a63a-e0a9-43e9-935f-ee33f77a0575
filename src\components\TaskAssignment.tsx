import React, { useState } from 'react';
import { FiU<PERSON><PERSON>, FiX, FiCheck } from 'react-icons/fi';
import UserAvatar from './UserAvatar';
import type { User } from '../App';

interface TaskAssignmentProps {
  assignedTo: string[];
  users: User[];
  onAssign: (userIds: string[]) => void;
  className?: string;
}

const TaskAssignment: React.FC<TaskAssignmentProps> = ({
  assignedTo,
  users,
  onAssign,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const assignedUsers = users.filter(user => assignedTo.includes(user.id));
  const availableUsers = users.filter(user => 
    !assignedTo.includes(user.id) && 
    user.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleToggleUser = (userId: string) => {
    const newAssignedTo = assignedTo.includes(userId)
      ? assignedTo.filter(id => id !== userId)
      : [...assignedTo, userId];
    onAssign(newAssignedTo);
  };

  const handleRemoveUser = (userId: string) => {
    const newAssignedTo = assignedTo.filter(id => id !== userId);
    onAssign(newAssignedTo);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Assignment Display */}
      <div className="flex items-center gap-2 mb-2">
        <FiUsers className="text-gray-400" />
        <span className="text-sm text-gray-300">Assigned to:</span>
        {assignedUsers.length === 0 ? (
          <span className="text-sm text-gray-500 italic">Unassigned</span>
        ) : (
          <div className="flex items-center gap-1">
            {assignedUsers.map(user => (
              <div key={user.id} className="flex items-center gap-1 bg-gray-700 rounded-full px-2 py-1">
                <UserAvatar user={user} size="sm" />
                <span className="text-xs text-gray-300">{user.name}</span>
                <button
                  onClick={() => handleRemoveUser(user.id)}
                  className="text-gray-400 hover:text-red-400 transition-colors"
                >
                  <FiX className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Assignment Dropdown */}
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 transition-colors flex items-center justify-between"
        >
          <span>Assign to team members</span>
          <FiUsers className="text-gray-400" />
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-10">
            {/* Search */}
            <div className="p-3 border-b border-gray-700">
              <input
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-gray-300 placeholder-gray-400 focus:outline-none focus:border-blue-500"
              />
            </div>

            {/* User List */}
            <div className="max-h-48 overflow-y-auto">
              {availableUsers.length === 0 ? (
                <div className="p-3 text-sm text-gray-400 text-center">
                  {searchTerm ? 'No users found' : 'No available users'}
                </div>
              ) : (
                availableUsers.map(user => (
                  <button
                    key={user.id}
                    onClick={() => handleToggleUser(user.id)}
                    className="w-full p-3 flex items-center gap-3 hover:bg-gray-700 transition-colors border-b border-gray-700 last:border-b-0"
                  >
                    <UserAvatar user={user} size="sm" />
                    <div className="flex-1 text-left">
                      <div className="text-sm text-gray-300 font-medium">{user.name}</div>
                      <div className="text-xs text-gray-400">{user.email}</div>
                    </div>
                    <FiCheck className="w-4 h-4 text-blue-500" />
                  </button>
                ))
              )}
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default TaskAssignment; 