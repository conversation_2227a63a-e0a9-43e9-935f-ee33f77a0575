import React from 'react';
import { FiCalendar, FiMessageCircle, FiCheck, FiFolder, FiAlertTriangle } from 'react-icons/fi';
import type { Task, Project } from '../App';

interface TaskCardProps {
  task: Task;
  project?: Project;
  onClick?: () => void;
  onToggleComplete?: (completed: boolean) => void;
}

const TaskCard: React.FC<TaskCardProps> = ({ task, project, onClick, onToggleComplete }) => {
  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && !task.completed;
  const isToday = task.dueDate && new Date(task.dueDate).toDateString() === new Date().toDateString();
  const isTomorrow = task.dueDate && new Date(task.dueDate).toDateString() === new Date(Date.now() + 86400000).toDateString();

  const getDueDateDisplay = () => {
    if (!task.dueDate) return null;
    
    const date = new Date(task.dueDate);
    const today = new Date();
    const diffTime = date.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (isToday) return 'Today';
    if (isTomorrow) return 'Tomorrow';
    if (diffDays < 0) return `${Math.abs(diffDays)} day${Math.abs(diffDays) !== 1 ? 's' : ''} ago`;
    if (diffDays <= 7) return `In ${diffDays} day${diffDays !== 1 ? 's' : ''}`;
    
    return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
  };

  const getDueDateColor = () => {
    if (isOverdue) return 'text-red-400';
    if (isToday) return 'text-orange-400';
    if (isTomorrow) return 'text-yellow-400';
    return 'text-gray-400';
  };

  return (
    <div
      className="flex items-start gap-3 bg-[#23272f] rounded-xl shadow-sm border border-[#23272f] hover:border-blue-600 transition py-3 px-4 mb-1 max-w-md w-full cursor-pointer group"
      onClick={onClick}
    >
      {/* Checkbox */}
      <button
        className={`flex-shrink-0 w-5 h-5 rounded-full border-2 border-gray-400 flex items-center justify-center transition-colors duration-150 bg-transparent group-hover:border-blue-500 ${task.completed ? 'border-blue-600 bg-blue-600' : ''}`}
        onClick={e => { e.stopPropagation(); onToggleComplete?.(!task.completed); }}
        aria-label={task.completed ? 'Mark as incomplete' : 'Mark as complete'}
      >
        {task.completed && <FiCheck className="text-white text-xs" />}
      </button>
      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className={`font-medium text-base ${task.completed ? 'line-through text-gray-500' : 'text-gray-100'}`}>{task.name}</div>
        {task.description && (
          <div className="text-sm text-gray-400 truncate mt-0.5">{task.description}</div>
        )}
        <div className="flex items-center gap-3 mt-2">
          {project && (
            <span className="flex items-center text-xs text-gray-400">
              <div 
                className="w-3 h-3 rounded mr-1"
                style={{ backgroundColor: project.color }}
              />
              {project.name}
            </span>
          )}
          {task.dueDate && (
            <span className={`flex items-center text-xs font-medium ${getDueDateColor()}`}>
              {isOverdue ? <FiAlertTriangle className="mr-1" /> : <FiCalendar className="mr-1" />}
              {getDueDateDisplay()}
            </span>
          )}
          {task.priority && task.priority !== 'Normal' && (
            <span className={`text-xs px-2 py-0.5 rounded-full ${
              task.priority === 'Urgent' ? 'bg-red-900 text-red-300' :
              task.priority === 'High' ? 'bg-orange-900 text-orange-300' :
              task.priority === 'Low' ? 'bg-gray-700 text-gray-300' :
              'bg-blue-900 text-blue-300'
            }`}>
              {task.priority}
            </span>
          )}
          {task.comments && task.comments.length > 0 && (
            <span className="flex items-center text-xs text-gray-500">
              <FiMessageCircle className="mr-1" />
              {task.comments.length}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskCard; 