import React, { useState, useRef, useEffect } from 'react';
import { FiX, FiInbox, FiCalendar, FiFlag, FiBell, FiLock, FiMapPin, FiFolder, FiPlus, FiTrash2 } from 'react-icons/fi';
import type { Task, Subtask, Project, Reminder, User, Comment } from '../App';
import ReminderForm from './ReminderForm';
import TaskAssignment from './TaskAssignment';
import CommentSection from './CommentSection';
import TaskSharing from './TaskSharing';

const PRIORITY_OPTIONS = [
  { label: 'Urgent', color: 'bg-red-700 text-red-100' },
  { label: 'High', color: 'bg-orange-600 text-orange-100' },
  { label: 'Normal', color: 'bg-blue-700 text-blue-100' },
  { label: 'Low', color: 'bg-gray-700 text-gray-100' },
];

interface TaskDetailModalProps {
  task: Task;
  onClose: () => void;
  onUpdateTask: (task: Task) => void;
  projects?: Project[];
  onAddReminder?: (taskId: string, reminder: Omit<Reminder, 'id' | 'taskId' | 'createdAt'>) => void;
  onUpdateReminder?: (taskId: string, reminderId: string, updates: Partial<Reminder>) => void;
  onDeleteReminder?: (taskId: string, reminderId: string) => void;
  // Collaboration props
  users?: User[];
  currentUser?: User;
  onAssignTask?: (taskId: string, userIds: string[]) => void;
  onAddComment?: (taskId: string, comment: Omit<Comment, 'id' | 'timestamp' | 'isEdited'>) => void;
  onUpdateComment?: (taskId: string, commentId: string, updates: Partial<Comment>) => void;
  onDeleteComment?: (taskId: string, commentId: string) => void;
  onShareTask?: (taskId: string, userIds: string[], visibility: 'private' | 'team' | 'public') => void;
}

const TaskDetailModal: React.FC<TaskDetailModalProps> = ({ 
  task, 
  onClose, 
  onUpdateTask, 
  projects = [],
  onAddReminder,
  onUpdateReminder,
  onDeleteReminder,
  users,
  currentUser,
  onAssignTask,
  onAddComment,
  onUpdateComment,
  onDeleteComment,
  onShareTask
}) => {
  const [showPriority, setShowPriority] = useState(false);
  const [showTagInput, setShowTagInput] = useState(false);
  const [tagInput, setTagInput] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [commentInput, setCommentInput] = useState('');
  const [subtaskInput, setSubtaskInput] = useState('');
  const [addingSubtask, setAddingSubtask] = useState(false);
  const [editingTitle, setEditingTitle] = useState(false);
  const [editingDescription, setEditingDescription] = useState(false);
  const [titleInput, setTitleInput] = useState(task.name);
  const [descriptionInput, setDescriptionInput] = useState(task.description || '');
  const [showProjectDropdown, setShowProjectDropdown] = useState(false);
  const [showReminderForm, setShowReminderForm] = useState(false);

  // Ref for tag popover
  const tagPopoverRef = useRef<HTMLDivElement>(null);
  const projectDropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!showTagInput) return;
    function handleClick(e: MouseEvent) {
      if (tagPopoverRef.current && !tagPopoverRef.current.contains(e.target as Node)) {
        setShowTagInput(false);
      }
    }
    function handleEscape(e: KeyboardEvent) {
      if (e.key === 'Escape') setShowTagInput(false);
    }
    document.addEventListener('mousedown', handleClick);
    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('mousedown', handleClick);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [showTagInput]);

  useEffect(() => {
    if (!showProjectDropdown) return;
    function handleClick(e: MouseEvent) {
      if (projectDropdownRef.current && !projectDropdownRef.current.contains(e.target as Node)) {
        setShowProjectDropdown(false);
      }
    }
    function handleEscape(e: KeyboardEvent) {
      if (e.key === 'Escape') setShowProjectDropdown(false);
    }
    document.addEventListener('mousedown', handleClick);
    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('mousedown', handleClick);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [showProjectDropdown]);

  useEffect(() => {
    setTitleInput(task.name);
    setDescriptionInput(task.description || '');
  }, [task]);

  useEffect(() => {
    function handleKeyDown(e: KeyboardEvent) {
      if (e.key === 'Escape') {
        onClose();
      }
    }
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  // Handlers for updating fields
  const updateField = (field: keyof Task, value: any) => {
    onUpdateTask({ ...task, [field]: value });
  };
  const handleAddTag = () => {
    if (tagInput.trim() && (!task.tags || !task.tags.includes(tagInput.trim()))) {
      updateField('tags', [...(task.tags || []), tagInput.trim()]);
      setTagInput('');
    }
  };
  const handleRemoveTag = (tag: string) => {
    updateField('tags', (task.tags || []).filter(t => t !== tag));
  };

  const handleAddComment = () => {
    if (!commentInput.trim()) return;
    if (onAddComment && currentUser) {
      onAddComment(task.id, {
        text: commentInput.trim(),
        author: currentUser.name,
        authorId: currentUser.id,
        mentions: []
      });
    }
    setCommentInput('');
  };

  const handleAddSubtask = () => {
    if (!subtaskInput.trim()) return;
    const newSubtask: Subtask = {
      id: Date.now().toString(),
      name: subtaskInput.trim(),
      completed: false,
    };
    onUpdateTask({
      ...task,
      subtasks: [...(task.subtasks || []), newSubtask],
    });
    setSubtaskInput('');
    setAddingSubtask(false);
  };
  const handleToggleSubtask = (id: string) => {
    onUpdateTask({
      ...task,
      subtasks: (task.subtasks || []).map((st) => st.id === id ? { ...st, completed: !st.completed } : st),
    });
  };
  const handleDeleteSubtask = (id: string) => {
    onUpdateTask({
      ...task,
      subtasks: (task.subtasks || []).filter((st) => st.id !== id),
    });
  };

  const handleTitleSave = () => {
    if (titleInput.trim() && titleInput !== task.name) {
      updateField('name', titleInput.trim());
    }
    setEditingTitle(false);
  };
  const handleDescriptionSave = () => {
    if (descriptionInput !== (task.description || '')) {
      updateField('description', descriptionInput);
    }
    setEditingDescription(false);
  };

  // Reminder management functions
  const handleAddReminder = (reminder: Omit<Reminder, 'id' | 'taskId' | 'createdAt'>) => {
    if (onAddReminder) {
      onAddReminder(task.id, reminder);
    }
    setShowReminderForm(false);
  };

  const handleToggleReminder = (reminderId: string) => {
    if (onUpdateReminder) {
      const reminder = task.reminders?.find(r => r.id === reminderId);
      if (reminder) {
        onUpdateReminder(task.id, reminderId, { isActive: !reminder.isActive });
      }
    }
  };

  const handleDeleteReminder = (reminderId: string) => {
    if (onDeleteReminder) {
      onDeleteReminder(task.id, reminderId);
    }
  };

  const currentProject = projects.find(p => p.id === task.projectId);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
      <div className="bg-[#191c22] rounded-xl shadow-lg w-full max-w-4xl flex flex-col md:flex-row relative border border-gray-700">
        {/* Left: Main content */}
        <div className="flex-1 p-8">
          <button className="absolute top-4 right-4 text-gray-400 hover:text-gray-200 text-2xl" onClick={onClose} aria-label="Close">
            <FiX />
          </button>
          {/* Task name and description */}
          <div className="flex items-center gap-2 mb-4">
            <span className="text-gray-400">
              {currentProject ? <FiFolder /> : <FiInbox />}
            </span>
            <span className="text-lg text-gray-400">
              {currentProject ? currentProject.name : 'Inbox'}
            </span>
          </div>
          {/* Editable Title */}
          {editingTitle ? (
            <input
              className="text-2xl font-bold text-gray-100 mb-2 bg-transparent border-b border-blue-500 focus:outline-none w-full"
              value={titleInput}
              autoFocus
              onChange={e => setTitleInput(e.target.value)}
              onBlur={handleTitleSave}
              onKeyDown={e => { if (e.key === 'Enter') handleTitleSave(); }}
              maxLength={100}
            />
          ) : (
            <div
              className="text-2xl font-bold text-gray-100 mb-2 cursor-pointer hover:underline"
              onClick={() => setEditingTitle(true)}
              title="Click to edit title"
            >
              {task.name}
            </div>
          )}
          {/* Editable Description */}
          {editingDescription ? (
            <textarea
              className="text-gray-400 mb-6 bg-transparent border-b border-blue-500 focus:outline-none w-full resize-none"
              value={descriptionInput}
              autoFocus
              rows={2}
              onChange={e => setDescriptionInput(e.target.value)}
              onBlur={handleDescriptionSave}
              onKeyDown={e => {
                if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) handleDescriptionSave();
              }}
              maxLength={300}
            />
          ) : (
            <div
              className="text-gray-400 mb-6 italic cursor-pointer hover:underline"
              onClick={() => setEditingDescription(true)}
              title="Click to edit description"
            >
              {task.description || <span className="italic">No description</span>}
            </div>
          )}
          {/* Subtasks UI - moved below description */}
          <div className="mb-6">
            {addingSubtask ? (
              <div className="flex items-center gap-2 mb-2">
                <input
                  className="bg-transparent border-b border-gray-700 px-2 py-1 text-gray-100 w-64 focus:outline-none"
                  placeholder="Sub-task name"
                  value={subtaskInput}
                  autoFocus
                  onChange={e => setSubtaskInput(e.target.value)}
                  onKeyDown={e => { if (e.key === 'Enter') handleAddSubtask(); if (e.key === 'Escape') { setAddingSubtask(false); setSubtaskInput(''); } }}
                  onBlur={handleAddSubtask}
                />
              </div>
            ) : (
              <button
                className="flex items-center gap-2 text-gray-300 hover:text-blue-400 font-semibold mb-2"
                onClick={() => setAddingSubtask(true)}
              >
                <span className="text-xl">+</span> Add sub-task
              </button>
            )}
            <div className="flex flex-col gap-2 mt-2">
              {(task.subtasks || []).map((st) => (
                <div key={st.id} className="flex items-center gap-2 group">
                  <input
                    type="checkbox"
                    checked={st.completed}
                    onChange={() => handleToggleSubtask(st.id)}
                    className="h-5 w-5 accent-blue-500 rounded border-gray-600 focus:ring-2 focus:ring-blue-400 transition-colors duration-150"
                  />
                  <span className={`flex-1 text-gray-200 transition-all duration-150 ${st.completed ? 'line-through opacity-50' : ''}`}>{st.name}</span>
                  <button className="text-gray-500 hover:text-red-400 opacity-0 group-hover:opacity-100 transition-opacity duration-150" onClick={() => handleDeleteSubtask(st.id)}><FiX /></button>
                </div>
              ))}
            </div>
          </div>
          <div className="flex items-center gap-3 mt-8 mb-4">
            <div className="bg-orange-500 rounded-full w-8 h-8 flex items-center justify-center font-bold text-lg">M</div>
            <input
              className="flex-1 bg-transparent border border-gray-700 rounded-full px-4 py-2 text-gray-200 placeholder-gray-500"
              placeholder="Comment"
              value={commentInput}
              onChange={e => setCommentInput(e.target.value)}
              onKeyDown={e => { if (e.key === 'Enter') handleAddComment(); }}
            />
            <button className="ml-2 text-gray-400 hover:text-gray-200" onClick={handleAddComment}>
              <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M16.5 8.25v-1.5A2.25 2.25 0 0014.25 4.5h-4.5A2.25 2.25 0 007.5 6.75v10.5A2.25 2.25 0 009.75 19.5h4.5a2.25 2.25 0 002.25-2.25v-1.5" /></svg>
            </button>
          </div>
        </div>
        {/* Right: Properties */}
        <div className="w-full md:w-80 bg-[#1e222a] border-l border-gray-700 p-8 flex flex-col gap-6">
          <div>
            <div className="text-xs text-gray-400 font-bold mb-1">Project</div>
            <div className="flex items-center gap-2 text-gray-200 relative" ref={projectDropdownRef}>
              <span>
                {currentProject ? <FiFolder /> : <FiInbox />}
              </span>
              <button
                className="underline hover:text-blue-400 flex items-center gap-2"
                onClick={() => setShowProjectDropdown(v => !v)}
              >
                {currentProject ? currentProject.name : 'Inbox'}
                <span className="text-xs">▼</span>
              </button>
              {showProjectDropdown && (
                <div className="absolute left-0 top-8 bg-gray-900 text-gray-100 rounded shadow border border-gray-700 z-20 min-w-[200px] max-h-60 overflow-y-auto">
                  <div
                    className="px-4 py-2 cursor-pointer hover:bg-gray-800 border-b border-gray-700"
                    onClick={() => { updateField('projectId', undefined); setShowProjectDropdown(false); }}
                  >
                    <div className="flex items-center gap-2">
                      <FiInbox size={14} />
                      <span>Inbox</span>
                    </div>
                  </div>
                  {projects.map(project => (
                    <div
                      key={project.id}
                      className="px-4 py-2 cursor-pointer hover:bg-gray-800"
                      onClick={() => { updateField('projectId', project.id); setShowProjectDropdown(false); }}
                    >
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded flex-shrink-0"
                          style={{ backgroundColor: project.color }}
                        />
                        <span className="truncate">{project.name}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400 font-bold mb-1">Date</div>
            <div className="flex items-center gap-2 text-gray-200">
              <span><FiCalendar /></span>
              <button
                className="underline hover:text-blue-400"
                onClick={() => setShowDatePicker(v => !v)}
              >
                {task.dueDate ? new Date(task.dueDate).toLocaleDateString() : <span className='italic'>No date</span>}
              </button>
              {task.dueDate && (
                <button className="ml-2 text-gray-400 hover:text-red-400" onClick={() => updateField('dueDate', undefined)}><FiX /></button>
              )}
              {showDatePicker && (
                <input
                  type="date"
                  className="ml-2 bg-gray-900 text-gray-100 rounded p-1 border border-gray-700 z-20"
                  value={task.dueDate || ''}
                  onChange={e => { updateField('dueDate', e.target.value); setShowDatePicker(false); }}
                  onBlur={() => setShowDatePicker(false)}
                  autoFocus
                />
              )}
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400 font-bold mb-1">Deadline</div>
            <div className="flex items-center gap-2 text-gray-200"><span><FiMapPin /></span> <span className="italic">(Not set)</span> <span className="ml-2 text-gray-500"><FiLock /></span></div>
          </div>
          <div>
            <div className="text-xs text-gray-400 font-bold mb-1">Priority</div>
            <div className="flex items-center gap-2 text-gray-200 relative">
              <span><FiFlag /></span>
              <button
                className={`px-2 py-1 rounded text-xs font-semibold ${PRIORITY_OPTIONS.find(p => p.label === task.priority)?.color || 'bg-gray-700 text-gray-100'}`}
                onClick={() => setShowPriority(v => !v)}
              >
                {task.priority || <span className='italic'>None</span>}
              </button>
              {showPriority && (
                <div className="absolute left-0 top-8 bg-gray-900 text-gray-100 rounded shadow border border-gray-700 z-20 min-w-[120px]">
                  {PRIORITY_OPTIONS.map(opt => (
                    <div
                      key={opt.label}
                      className={`px-4 py-2 cursor-pointer hover:bg-gray-800 ${opt.label === task.priority ? 'font-bold' : ''}`}
                      onClick={() => { updateField('priority', opt.label); setShowPriority(false); }}
                    >
                      {opt.label}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400 font-bold mb-1">Labels</div>
            <div className="flex flex-wrap gap-2 mt-1 relative">
              {task.tags && task.tags.length > 0 ? (
                task.tags.map(tag => (
                  <span key={tag} className="bg-blue-800 text-blue-200 text-xs px-2 py-0.5 rounded-full flex items-center gap-1">
                    {tag}
                    <button type="button" className="ml-1" onClick={() => handleRemoveTag(tag)}><FiX /></button>
                  </span>
                ))
              ) : (
                <span className="italic text-gray-500">None</span>
              )}
              <button className="ml-2 text-gray-400 hover:text-blue-400" onClick={() => setShowTagInput(v => !v)}><span className="text-lg">+</span></button>
              {showTagInput && (
                <div ref={tagPopoverRef} className="absolute left-0 top-8 bg-gray-900 text-gray-100 rounded shadow border border-gray-700 z-20 p-2 min-w-[180px]">
                  <div className="flex gap-2">
                    <input
                      className="bg-gray-800 border border-gray-700 rounded px-2 py-1 text-xs text-gray-100 flex-1"
                      placeholder="Add tag"
                      value={tagInput}
                      onChange={e => setTagInput(e.target.value)}
                      onKeyDown={e => { if (e.key === 'Enter') { e.preventDefault(); handleAddTag(); } }}
                      autoFocus
                    />
                    <button type="button" className="bg-blue-700 text-white rounded px-2 text-xs" onClick={handleAddTag}>Add</button>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400 font-bold mb-1">Reminders</div>
            <div className="space-y-2">
              {(task.reminders || []).map(reminder => (
                <div key={reminder.id} className="flex items-center justify-between bg-gray-800 rounded-md px-3 py-2">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={reminder.isActive}
                      onChange={() => handleToggleReminder(reminder.id)}
                      className="h-4 w-4 accent-blue-500 rounded border-gray-600 focus:ring-2 focus:ring-blue-400 mr-2"
                    />
                    <div>
                      <div className="text-sm text-gray-300">
                        {new Date(reminder.time).toLocaleString()}
                      </div>
                      {reminder.message && (
                        <div className="text-xs text-gray-400">{reminder.message}</div>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={() => handleDeleteReminder(reminder.id)}
                    className="text-gray-400 hover:text-red-400 ml-2"
                  >
                    <FiTrash2 size={14} />
                  </button>
                </div>
              ))}
              <button
                onClick={() => setShowReminderForm(true)}
                className="flex items-center gap-2 text-gray-400 hover:text-blue-400 text-sm"
              >
                <FiPlus size={14} />
                Add Reminder
              </button>
            </div>
          </div>

          {/* Collaboration Section */}
          {users && currentUser && (
            <>
              {/* Task Assignment */}
              {onAssignTask && (
                <div>
                  <div className="text-xs text-gray-400 font-bold mb-1">Assignment</div>
                  <TaskAssignment
                    assignedTo={task.assignedTo || []}
                    users={users}
                    onAssign={(userIds) => onAssignTask(task.id, userIds)}
                  />
                </div>
              )}

              {/* Task Sharing */}
              {onShareTask && (
                <div>
                  <div className="text-xs text-gray-400 font-bold mb-1">Sharing</div>
                  <TaskSharing
                    sharedWith={task.sharedWith || []}
                    visibility={task.visibility || 'private'}
                    users={users}
                    onShare={(userIds, visibility) => onShareTask(task.id, userIds, visibility)}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Comments Section - Full Width */}
      {users && currentUser && onAddComment && onUpdateComment && onDeleteComment && (
        <div className="bg-[#191c22] border-t border-gray-700 p-8">
          <CommentSection
            comments={task.comments || []}
            users={users}
            currentUser={currentUser}
            onAddComment={(comment) => onAddComment(task.id, comment)}
            onUpdateComment={(commentId, updates) => onUpdateComment(task.id, commentId, updates)}
            onDeleteComment={(commentId) => onDeleteComment(task.id, commentId)}
          />
        </div>
      )}

      {/* Reminder Form Modal */}
      {showReminderForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
          <div className="relative max-w-md w-full mx-4">
            <ReminderForm
              onAddReminder={handleAddReminder}
              onCancel={() => setShowReminderForm(false)}
              defaultType={task.dueDate ? 'due_date' : 'custom'}
              defaultTime={task.dueDate ? new Date(task.dueDate).toISOString() : undefined}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskDetailModal; 