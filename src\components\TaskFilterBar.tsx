import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Arrow<PERSON>p, FiCalendar, FiClock, FiAlertTriangle } from 'react-icons/fi';
import type { Task } from '../App';

export type DueDateFilter = 'all' | 'today' | 'tomorrow' | 'this-week' | 'overdue' | 'no-due-date';
export type SortOption = 'due-date' | 'priority' | 'name' | 'created' | 'project';

interface TaskFilterBarProps {
  tasks: Task[];
  onFilterChange: (filteredTasks: Task[]) => void;
  className?: string;
}

const TaskFilterBar: React.FC<TaskFilterBarProps> = ({ tasks, onFilterChange, className = '' }) => {
  const [dueDateFilter, setDueDateFilter] = useState<DueDateFilter>('all');
  const [sortBy, setSortBy] = useState<SortOption>('due-date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Priority order for sorting
  const priorityOrder = { 'Urgent': 4, 'High': 3, 'Normal': 2, 'Low': 1 };

  // Filter and sort tasks
  React.useEffect(() => {
    let filteredTasks = [...tasks];

    // Apply due date filter
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    switch (dueDateFilter) {
      case 'today':
        filteredTasks = filteredTasks.filter(task => {
          if (!task.dueDate) return false;
          const taskDate = new Date(task.dueDate);
          taskDate.setHours(0, 0, 0, 0);
          return taskDate.getTime() === today.getTime();
        });
        break;
      case 'tomorrow':
        filteredTasks = filteredTasks.filter(task => {
          if (!task.dueDate) return false;
          const taskDate = new Date(task.dueDate);
          taskDate.setHours(0, 0, 0, 0);
          return taskDate.getTime() === tomorrow.getTime();
        });
        break;
      case 'this-week':
        filteredTasks = filteredTasks.filter(task => {
          if (!task.dueDate) return false;
          const taskDate = new Date(task.dueDate);
          taskDate.setHours(0, 0, 0, 0);
          return taskDate >= today && taskDate < nextWeek;
        });
        break;
      case 'overdue':
        filteredTasks = filteredTasks.filter(task => {
          if (!task.dueDate) return false;
          const taskDate = new Date(task.dueDate);
          taskDate.setHours(0, 0, 0, 0);
          return taskDate < today && !task.completed;
        });
        break;
      case 'no-due-date':
        filteredTasks = filteredTasks.filter(task => !task.dueDate);
        break;
      default:
        // 'all' - no filtering
        break;
    }

    // Apply sorting
    filteredTasks.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'due-date':
          if (!a.dueDate && !b.dueDate) comparison = 0;
          else if (!a.dueDate) comparison = 1;
          else if (!b.dueDate) comparison = -1;
          else comparison = new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
          break;
        case 'priority':
          const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 0;
          const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 0;
          comparison = bPriority - aPriority; // Higher priority first
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'created':
          // Assuming tasks have creation timestamps, using id as fallback
          comparison = parseInt(a.id) - parseInt(b.id);
          break;
        case 'project':
          if (!a.projectId && !b.projectId) comparison = 0;
          else if (!a.projectId) comparison = 1;
          else if (!b.projectId) comparison = -1;
          else comparison = a.projectId.localeCompare(b.projectId);
          break;
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });

    onFilterChange(filteredTasks);
  }, [tasks, dueDateFilter, sortBy, sortDirection, onFilterChange]);

  const getFilterIcon = (filter: DueDateFilter) => {
    switch (filter) {
      case 'today':
      case 'tomorrow':
      case 'this-week':
        return <FiCalendar className="w-4 h-4" />;
      case 'overdue':
        return <FiAlertTriangle className="w-4 h-4" />;
      case 'no-due-date':
        return <FiClock className="w-4 h-4" />;
      default:
        return <FiFilter className="w-4 h-4" />;
    }
  };

  const getFilterLabel = (filter: DueDateFilter) => {
    switch (filter) {
      case 'all': return 'All Tasks';
      case 'today': return 'Today';
      case 'tomorrow': return 'Tomorrow';
      case 'this-week': return 'This Week';
      case 'overdue': return 'Overdue';
      case 'no-due-date': return 'No Due Date';
    }
  };

  const getSortLabel = (sort: SortOption) => {
    switch (sort) {
      case 'due-date': return 'Due Date';
      case 'priority': return 'Priority';
      case 'name': return 'Name';
      case 'created': return 'Created';
      case 'project': return 'Project';
    }
  };

  return (
    <div className={`flex flex-wrap items-center gap-3 ${className}`}>
      {/* Due Date Filter */}
      <div className="relative">
        <select
          value={dueDateFilter}
          onChange={(e) => setDueDateFilter(e.target.value as DueDateFilter)}
          className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-gray-100 text-sm focus:outline-none focus:border-blue-500 appearance-none pr-8"
        >
          <option value="all">All Tasks</option>
          <option value="today">Today</option>
          <option value="tomorrow">Tomorrow</option>
          <option value="this-week">This Week</option>
          <option value="overdue">Overdue</option>
          <option value="no-due-date">No Due Date</option>
        </select>
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
          {getFilterIcon(dueDateFilter)}
        </div>
      </div>

      {/* Sort By */}
      <div className="relative">
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as SortOption)}
          className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-gray-100 text-sm focus:outline-none focus:border-blue-500 appearance-none pr-8"
        >
          <option value="due-date">Due Date</option>
          <option value="priority">Priority</option>
          <option value="name">Name</option>
          <option value="created">Created</option>
          <option value="project">Project</option>
        </select>
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
          <FiArrowUp className="w-4 h-4" />
        </div>
      </div>

      {/* Sort Direction */}
      <button
        onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
        className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-gray-100 text-sm hover:bg-gray-700 transition-colors"
        title={`Sort ${sortDirection === 'asc' ? 'Descending' : 'Ascending'}`}
      >
        <FiArrowUp className={`w-4 h-4 ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
      </button>

      {/* Active filters display */}
      <div className="flex items-center gap-2 text-sm text-gray-400">
        <span>Showing:</span>
        <span className="bg-gray-700 px-2 py-1 rounded text-gray-300">
          {getFilterLabel(dueDateFilter)}
        </span>
        <span>sorted by</span>
        <span className="bg-gray-700 px-2 py-1 rounded text-gray-300">
          {getSortLabel(sortBy)}
        </span>
      </div>
    </div>
  );
};

export default TaskFilterBar; 