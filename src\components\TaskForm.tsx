import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>lag, FiBell, FiTag, FiMoreHorizontal, FiInbox, FiX, FiCalendar, FiFolder } from 'react-icons/fi';
import type { Task, Project, Reminder } from '../App';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import ReminderForm from './ReminderForm';

const PRIORITY_OPTIONS = [
  { label: 'Urgent', color: 'bg-red-700 text-red-100' },
  { label: 'High', color: 'bg-orange-600 text-orange-100' },
  { label: 'Normal', color: 'bg-blue-700 text-blue-100' },
  { label: 'Low', color: 'bg-gray-700 text-gray-100' },
];

type TaskFormProps = {
  onAddTask?: (task: Omit<Task, 'id'>) => void;
  projects?: Project[];
  defaultProjectId?: string;
  onCancel?: () => void;
  onAddReminder?: (taskId: string, reminder: Omit<Reminder, 'id' | 'taskId' | 'createdAt'>) => void;
};

const TaskForm: React.FC<TaskFormProps> = ({ onAddTask, projects = [], defaultProjectId, onCancel, onAddReminder }) => {
  const [taskName, setTaskName] = useState('');
  const [description, setDescription] = useState('');
  const [dueDate, setDueDate] = useState<string | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [priority, setPriority] = useState<string>('Normal');
  const [showPriority, setShowPriority] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [showTagInput, setShowTagInput] = useState(false);
  const [tagInput, setTagInput] = useState('');
  const [showMore, setShowMore] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string | undefined>(defaultProjectId);
  const [showProjectDropdown, setShowProjectDropdown] = useState(false);
  const [showReminderForm, setShowReminderForm] = useState(false);
  const [reminders, setReminders] = useState<Omit<Reminder, 'id' | 'taskId' | 'createdAt'>[]>([]);

  // Ref for tag popover
  const tagPopoverRef = useRef<HTMLDivElement>(null);
  const projectDropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelectedProjectId(defaultProjectId);
  }, [defaultProjectId]);

  useEffect(() => {
    if (!showTagInput) return;
    function handleClick(e: MouseEvent) {
      if (tagPopoverRef.current && !tagPopoverRef.current.contains(e.target as Node)) {
        setShowTagInput(false);
      }
    }
    function handleEscape(e: KeyboardEvent) {
      if (e.key === 'Escape') setShowTagInput(false);
    }
    document.addEventListener('mousedown', handleClick);
    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('mousedown', handleClick);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [showTagInput]);

  useEffect(() => {
    if (!showProjectDropdown) return;
    function handleClick(e: MouseEvent) {
      if (projectDropdownRef.current && !projectDropdownRef.current.contains(e.target as Node)) {
        setShowProjectDropdown(false);
      }
    }
    function handleEscape(e: KeyboardEvent) {
      if (e.key === 'Escape') setShowProjectDropdown(false);
    }
    document.addEventListener('mousedown', handleClick);
    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('mousedown', handleClick);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [showProjectDropdown]);

  useEffect(() => {
    function handleKeyDown(e: KeyboardEvent) {
      if (e.key === 'Escape') {
        if (onCancel) {
          onCancel();
        } else {
          setTaskName('');
          setDescription('');
          setDueDate(null);
          setPriority('Normal');
          setTags([]);
          setTagInput('');
          setSelectedProjectId(undefined);
          setReminders([]);
        }
      }
    }
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onCancel]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!taskName) return;
    
    const newTask = {
      name: taskName,
      description,
      dueToday: dueDate ? false : true, // legacy, not used in new UI
      dueDate: dueDate || undefined,
      priority,
      tags,
      projectId: selectedProjectId,
      reminders: reminders,
      // Collaboration fields
      assignedTo: [],
      createdBy: 'user1', // Default to current user
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isShared: false,
      visibility: 'private'
    } as any;

    onAddTask?.(newTask);
    
    // Add reminders if any
    if (reminders.length > 0 && onAddReminder) {
      // We'll need to get the task ID after it's created
      // For now, we'll handle this in the parent component
    }
    
    setTaskName('');
    setDescription('');
    setDueDate(null);
    setPriority('Normal');
    setTags([]);
    setTagInput('');
    setSelectedProjectId(undefined);
    setReminders([]);
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag));
  };

  const handleAddReminder = (reminder: Omit<Reminder, 'id' | 'taskId' | 'createdAt'>) => {
    setReminders(prev => [...prev, reminder]);
    setShowReminderForm(false);
  };

  const handleRemoveReminder = (index: number) => {
    console.log('Removing reminder at index', index);
    setReminders(prev => prev.filter((_, i) => i !== index));
  };

  const selectedProject = projects.find(p => p.id === selectedProjectId);

  return (
    <>
      <form onSubmit={handleSubmit} className="bg-[#191c22] rounded-xl p-6 shadow-md w-full max-w-2xl border border-gray-700">
        <div className="mb-4">
          <input
            className="bg-transparent text-2xl font-semibold w-full outline-none placeholder-gray-400"
            placeholder="Task name"
            value={taskName}
            onChange={e => setTaskName(e.target.value)}
          />
          <input
            className="bg-transparent text-gray-400 w-full mt-1 outline-none placeholder-gray-500"
            placeholder="Description"
            value={description}
            onChange={e => setDescription(e.target.value)}
          />
        </div>
        <div className="flex flex-wrap gap-2 mb-4">
          {/* Due Date */}
          <div className="relative">
            <DatePicker
              selected={dueDate ? new Date(dueDate) : null}
              onChange={date => setDueDate(date ? date.toISOString().split('T')[0] : null)}
              customInput={
                <button
                  type="button"
                  className={`flex items-center px-3 py-1 rounded-full text-sm ${dueDate ? 'bg-green-900 text-green-300' : 'bg-gray-800 text-gray-300'}`}
                >
                  <span className="mr-1"><FiCalendar /></span>
                  {dueDate ? new Date(dueDate).toLocaleDateString() : 'Due Date'}
                  {dueDate && (
                    <span className="ml-2 cursor-pointer" onClick={e => { e.stopPropagation(); setDueDate(null); }}> <FiX /> </span>
                  )}
                </button>
              }
              dateFormat="yyyy-MM-dd"
              placeholderText="Due Date"
              calendarClassName="bg-gray-900 text-gray-100 border border-gray-700 rounded shadow"
              popperPlacement="bottom"
              showPopperArrow={false}
              isClearable
              todayButton="Today"
              autoComplete="off"
              wrapperClassName="inline-block"
            />
          </div>
          {/* Priority */}
          <div className="relative">
            <button
              type="button"
              className={`flex items-center px-3 py-1 rounded-full text-sm ${PRIORITY_OPTIONS.find(p => p.label === priority)?.color || 'bg-gray-800 text-gray-300'}`}
              onClick={() => setShowPriority(v => !v)}
            >
              <span className="mr-1"><FiFlag /></span>
              Priority
              <span className="ml-2">{priority}</span>
            </button>
            {showPriority && (
              <div className="absolute left-0 top-10 bg-gray-900 text-gray-100 rounded shadow border border-gray-700 z-20 min-w-[120px]">
                {PRIORITY_OPTIONS.map(opt => (
                  <div
                    key={opt.label}
                    className={`px-4 py-2 cursor-pointer hover:bg-gray-800 ${opt.label === priority ? 'font-bold' : ''}`}
                    onClick={() => { setPriority(opt.label); setShowPriority(false); }}
                  >
                    {opt.label}
                  </div>
                ))}
              </div>
            )}
          </div>
          {/* Project Selection */}
          <div>
            <select
              value={selectedProjectId || ''}
              onChange={e => setSelectedProjectId(e.target.value || undefined)}
              className="flex items-center px-3 py-1 rounded-full text-sm bg-gray-800 text-gray-300 border border-gray-700 focus:outline-none focus:border-blue-500"
            >
              <option value="">Inbox</option>
              {projects.map(project => (
                <option key={project.id} value={project.id}>
                  {project.name}
                </option>
              ))}
            </select>
          </div>
          {/* Reminders */}
          <div className="relative">
            <button
              type="button"
              className={`flex items-center px-3 py-1 rounded-full text-sm ${reminders.length > 0 ? 'bg-blue-900 text-blue-300' : 'bg-gray-800 text-gray-300'}`}
              onClick={() => setShowReminderForm(true)}
            >
              <span className="mr-1"><FiBell /></span>
              Reminders
              {reminders.length > 0 && (
                <span className="ml-2 bg-blue-700 text-blue-200 text-xs px-1.5 py-0.5 rounded-full">
                  {reminders.length}
                </span>
              )}
            </button>
          </div>
          {/* Tags */}
          <div className="relative">
            <button
              type="button"
              className="flex items-center bg-gray-800 text-gray-300 px-3 py-1 rounded-full text-sm"
              onClick={() => setShowTagInput(v => !v)}
            >
              <span className="mr-1"><FiTag /></span> Tags
            </button>
            {showTagInput && (
              <div ref={tagPopoverRef} className="absolute left-0 top-10 bg-gray-900 text-gray-100 rounded shadow border border-gray-700 z-20 p-2 min-w-[180px]">
                <div className="flex gap-2 mb-2 flex-wrap">
                  {tags.map(tag => (
                    <span key={tag} className="bg-blue-800 text-blue-200 text-xs px-2 py-0.5 rounded-full flex items-center gap-1">
                      {tag}
                      <button type="button" className="ml-1" onClick={() => handleRemoveTag(tag)}><FiX /></button>
                    </span>
                  ))}
                </div>
                <div className="flex gap-2">
                  <input
                    className="bg-gray-800 border border-gray-700 rounded px-2 py-1 text-xs text-gray-100 flex-1"
                    placeholder="Add tag"
                    value={tagInput}
                    onChange={e => setTagInput(e.target.value)}
                    onKeyDown={e => { if (e.key === 'Enter') { e.preventDefault(); handleAddTag(); } }}
                  />
                  <button type="button" className="bg-blue-700 text-white rounded px-2 text-xs" onClick={handleAddTag}>Add</button>
                </div>
              </div>
            )}
          </div>
          {/* More options */}
          <button className="flex items-center bg-gray-800 text-gray-300 px-2 py-1 rounded-full text-sm" type="button" onClick={() => setShowMore(!showMore)}>
            <FiMoreHorizontal />
          </button>
        </div>
        {showMore && (
          <div className="mb-4 text-gray-400">More options coming soon...</div>
        )}
        
        {/* Display existing reminders */}
        {reminders.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-300 mb-2">Reminders:</h4>
            <div className="space-y-2">
              {reminders.map((reminder, index) => (
                <div key={reminder.time + (reminder.message || '') + index} className="flex items-center justify-between bg-gray-800 rounded-md px-3 py-2">
                  <div className="flex items-center">
                    <FiBell className="text-blue-400 mr-2" />
                    <span className="text-sm text-gray-300">
                      {new Date(reminder.time).toLocaleString()}
                    </span>
                    {reminder.message && (
                      <span className="text-sm text-gray-400 ml-2">- {reminder.message}</span>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemoveReminder(index)}
                    className="text-gray-400 hover:text-red-400"
                  >
                    <FiX />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex items-center justify-between border-t border-gray-700 pt-4 mt-4">
          <div className="flex items-center gap-2">
            <span className="text-gray-400">
              {selectedProject ? (
                <div 
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: selectedProject.color }}
                />
              ) : (
                <FiInbox />
              )}
            </span>
            <span className="text-gray-300">
              {selectedProject ? selectedProject.name : 'Inbox'}
            </span>
          </div>
          <div className="flex gap-2">
            <button className="px-4 py-2 rounded bg-gray-700 text-gray-200 font-semibold" type="button" onClick={onCancel ? onCancel : () => { setTaskName(''); setDescription(''); setDueDate(null); setPriority('Normal'); setTags([]); setTagInput(''); setSelectedProjectId(undefined); setReminders([]); }}>
              Cancel
            </button>
            <button
              className={`px-4 py-2 rounded font-semibold ${taskName ? 'bg-red-800 text-red-100 hover:bg-red-700' : 'bg-gray-800 text-gray-500 cursor-not-allowed'}`}
              disabled={!taskName}
              type="submit"
            >
              Add task
            </button>
          </div>
        </div>
      </form>
      {showReminderForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
          <div className="relative max-w-md w-full mx-4">
            <ReminderForm
              onAddReminder={handleAddReminder}
              onCancel={() => setShowReminderForm(false)}
              defaultType={dueDate ? 'due_date' : 'custom'}
              defaultTime={dueDate ? new Date(dueDate).toISOString() : undefined}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default TaskForm; 