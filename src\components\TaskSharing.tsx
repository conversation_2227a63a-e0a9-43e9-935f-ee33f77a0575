import React, { useState } from 'react';
import { FiShare2, <PERSON><PERSON><PERSON><PERSON>, FiGlobe, FiLock, FiMail } from 'react-icons/fi';
import UserAvatar from './UserAvatar';
import type { User } from '../App';

interface TaskSharingProps {
  sharedWith: string[];
  visibility: 'private' | 'team' | 'public';
  users: User[];
  onShare: (userIds: string[], visibility: 'private' | 'team' | 'public') => void;
  className?: string;
}

const TaskSharing: React.FC<TaskSharingProps> = ({
  sharedWith,
  visibility,
  users,
  onShare,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>(sharedWith);
  const [selectedVisibility, setSelectedVisibility] = useState<'private' | 'team' | 'public'>(visibility);
  const [searchTerm, setSearchTerm] = useState('');

  const sharedUsers = users.filter(user => sharedWith.includes(user.id));
  const availableUsers = users.filter(user => 
    !sharedWith.includes(user.id) && 
    user.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSave = () => {
    onShare(selectedUsers, selectedVisibility);
    setIsOpen(false);
  };

  const handleCancel = () => {
    setSelectedUsers(sharedWith);
    setSelectedVisibility(visibility);
    setIsOpen(false);
  };

  const handleToggleUser = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleRemoveUser = (userId: string) => {
    setSelectedUsers(prev => prev.filter(id => id !== userId));
  };

  const getVisibilityIcon = (vis: 'private' | 'team' | 'public') => {
    switch (vis) {
      case 'private':
        return <FiLock className="w-4 h-4" />;
      case 'team':
        return <FiUsers className="w-4 h-4" />;
      case 'public':
        return <FiGlobe className="w-4 h-4" />;
    }
  };

  const getVisibilityLabel = (vis: 'private' | 'team' | 'public') => {
    switch (vis) {
      case 'private':
        return 'Private';
      case 'team':
        return 'Team';
      case 'public':
        return 'Public';
    }
  };

  const getVisibilityDescription = (vis: 'private' | 'team' | 'public') => {
    switch (vis) {
      case 'private':
        return 'Only you can see this task';
      case 'team':
        return 'Visible to all team members';
      case 'public':
        return 'Visible to anyone with the link';
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Sharing Display */}
      <div className="flex items-center gap-2 mb-2">
        <FiShare2 className="text-gray-400" />
        <span className="text-sm text-gray-300">Shared with:</span>
        <div className="flex items-center gap-1">
          <span className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs ${
            visibility === 'private' ? 'bg-gray-700 text-gray-300' :
            visibility === 'team' ? 'bg-blue-900 text-blue-300' :
            'bg-green-900 text-green-300'
          }`}>
            {getVisibilityIcon(visibility)}
            {getVisibilityLabel(visibility)}
          </span>
          {sharedUsers.length > 0 && (
            <span className="text-xs text-gray-400">+ {sharedUsers.length} people</span>
          )}
        </div>
      </div>

      {/* Share Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 transition-colors flex items-center justify-between"
      >
        <span>Share task</span>
        <FiShare2 className="text-gray-400" />
      </button>

      {/* Sharing Modal */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
          <div className="bg-gray-900 rounded-xl p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-100">Share Task</h3>
              <button
                onClick={handleCancel}
                className="text-gray-400 hover:text-gray-200 text-2xl"
              >
                &times;
              </button>
            </div>

            {/* Visibility Settings */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-300 mb-3">Visibility</h4>
              <div className="space-y-2">
                {(['private', 'team', 'public'] as const).map((vis) => (
                  <label
                    key={vis}
                    className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedVisibility === vis
                        ? 'border-blue-500 bg-blue-900 bg-opacity-20'
                        : 'border-gray-700 hover:border-gray-600'
                    }`}
                  >
                    <input
                      type="radio"
                      name="visibility"
                      value={vis}
                      checked={selectedVisibility === vis}
                      onChange={() => setSelectedVisibility(vis)}
                      className="sr-only"
                    />
                    <div className={`p-1 rounded ${
                      selectedVisibility === vis ? 'text-blue-400' : 'text-gray-400'
                    }`}>
                      {getVisibilityIcon(vis)}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-300">
                        {getVisibilityLabel(vis)}
                      </div>
                      <div className="text-xs text-gray-400">
                        {getVisibilityDescription(vis)}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* User Selection */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-300 mb-3">Share with specific people</h4>
              
              {/* Selected Users */}
              {selectedUsers.length > 0 && (
                <div className="mb-3">
                  <div className="flex flex-wrap gap-1">
                    {users.filter(user => selectedUsers.includes(user.id)).map(user => (
                      <div key={user.id} className="flex items-center gap-1 bg-gray-700 rounded-full px-2 py-1">
                        <UserAvatar user={user} size="sm" />
                        <span className="text-xs text-gray-300">{user.name}</span>
                        <button
                          onClick={() => handleRemoveUser(user.id)}
                          className="text-gray-400 hover:text-red-400 transition-colors"
                        >
                          &times;
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* User Search */}
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-gray-300 placeholder-gray-400 focus:outline-none focus:border-blue-500"
                />
              </div>

              {/* Available Users */}
              {searchTerm && availableUsers.length > 0 && (
                <div className="mt-2 max-h-32 overflow-y-auto border border-gray-700 rounded">
                  {availableUsers.map(user => (
                    <button
                      key={user.id}
                      onClick={() => handleToggleUser(user.id)}
                      className="w-full p-2 flex items-center gap-3 hover:bg-gray-700 transition-colors border-b border-gray-700 last:border-b-0"
                    >
                      <UserAvatar user={user} size="sm" />
                      <div className="flex-1 text-left">
                        <div className="text-sm text-gray-300 font-medium">{user.name}</div>
                        <div className="text-xs text-gray-400">{user.email}</div>
                      </div>
                      <div className="w-4 h-4 border border-gray-600 rounded flex items-center justify-center">
                        {selectedUsers.includes(user.id) && (
                          <div className="w-2 h-2 bg-blue-500 rounded"></div>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <button
                onClick={handleCancel}
                className="flex-1 px-4 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Share
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskSharing; 