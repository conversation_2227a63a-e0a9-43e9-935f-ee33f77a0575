import React from 'react';
import { FiUser } from 'react-icons/fi';
import type { User } from '../App';

interface UserAvatarProps {
  user: User;
  size?: 'sm' | 'md' | 'lg';
  showOnlineStatus?: boolean;
  className?: string;
}

const UserAvatar: React.FC<UserAvatarProps> = ({ 
  user, 
  size = 'md', 
  showOnlineStatus = true,
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const statusSizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-2.5 h-2.5',
    lg: 'w-3 h-3'
  };

  return (
    <div className={`relative inline-block ${className}`}>
      {user.avatar ? (
        <img
          src={user.avatar}
          alt={user.name}
          className={`${sizeClasses[size]} rounded-full object-cover border-2 border-gray-700`}
        />
      ) : (
        <div className={`${sizeClasses[size]} rounded-full bg-gray-600 flex items-center justify-center border-2 border-gray-700`}>
          <FiUser className="text-gray-300" />
        </div>
      )}
      
      {showOnlineStatus && (
        <div className={`absolute -bottom-0.5 -right-0.5 ${statusSizeClasses[size]} rounded-full border-2 border-gray-900 ${
          user.isOnline ? 'bg-green-500' : 'bg-gray-500'
        }`} />
      )}
    </div>
  );
};

export default UserAvatar; 