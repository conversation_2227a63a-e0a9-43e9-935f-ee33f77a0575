import React from 'react';
import { FiList, FiGrid } from 'react-icons/fi';

export type ViewMode = 'list' | 'kanban';

interface ViewToggleProps {
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
  className?: string;
}

const ViewToggle: React.FC<ViewToggleProps> = ({ 
  currentView, 
  onViewChange, 
  className = '' 
}) => {
  return (
    <div className={`flex items-center bg-gray-800 rounded-lg p-1 ${className}`}>
      <button
        onClick={() => onViewChange('list')}
        className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
          currentView === 'list'
            ? 'bg-gray-700 text-gray-200'
            : 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
        }`}
        title="List view"
      >
        <FiList size={16} />
        <span className="hidden sm:inline">List</span>
      </button>
      
      <button
        onClick={() => onViewChange('kanban')}
        className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
          currentView === 'kanban'
            ? 'bg-gray-700 text-gray-200'
            : 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
        }`}
        title="Kanban view"
      >
        <FiGrid size={16} />
        <span className="hidden sm:inline">Kanban</span>
      </button>
    </div>
  );
};

export default ViewToggle; 