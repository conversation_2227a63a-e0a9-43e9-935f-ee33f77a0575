import React, { useState, useEffect, useRef } from 'react';
import Chat<PERSON><PERSON>bar from './ChatSidebar';
import ChatMessageArea from './ChatMessageArea';
import ChatInput from './ChatInput';
import ChatThreadPanel from './ChatThreadPanel';
import { ChatChannel, ChatMessage, ChatThread } from '../../types/chat';

// Import types from App.tsx
type TeamMember = {
  userId: string;
  projectId: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  joinedAt: string;
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canAssign: boolean;
    canComment: boolean;
    canShare: boolean;
  };
};

interface ChatInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
  // Integration props
  currentProjectId?: string;
  currentTaskId?: string;
  currentUserId: string;
  users: any[]; // Your existing User type
  projects: any[]; // Your existing Project type
  tasks: any[]; // Your existing Task type
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  isOpen,
  onClose,
  currentProjectId,
  currentTaskId,
  currentUserId,
  users,
  projects,
  tasks
}) => {
  const [activeChannelId, setActiveChannelId] = useState<string>('');
  const [activeThreadId, setActiveThreadId] = useState<string>('');
  const [showThreadPanel, setShowThreadPanel] = useState(false);
  const [channels, setChannels] = useState<ChatChannel[]>([]);
  const [messages, setMessages] = useState<{ [channelId: string]: ChatMessage[] }>({});
  const [threads, setThreads] = useState<{ [threadId: string]: ChatThread }>({});
  const [unreadCounts, setUnreadCounts] = useState<{ [channelId: string]: number }>({});

  // Auto-create channels based on current context
  useEffect(() => {
    const defaultChannels: ChatChannel[] = [
      {
        id: 'general',
        name: 'general',
        description: 'General team discussions',
        type: 'public',
        icon: '💬',
        color: '#4f46e5',
        createdAt: new Date().toISOString(),
        createdBy: currentUserId,
        lastActivity: new Date().toISOString(),
        memberCount: users.length,
        isArchived: false,
        isMuted: false,
        settings: {
          allowFileUploads: true,
          allowThreads: true,
          allowReactions: true,
          requireApproval: false,
          autoArchive: false,
        },
        members: users.map(u => u.id),
        admins: [currentUserId],
      },
      {
        id: 'random',
        name: 'random',
        description: 'Non-work related discussions',
        type: 'public',
        icon: '🎲',
        color: '#10b981',
        createdAt: new Date().toISOString(),
        createdBy: currentUserId,
        lastActivity: new Date().toISOString(),
        memberCount: users.length,
        isArchived: false,
        isMuted: false,
        settings: {
          allowFileUploads: true,
          allowThreads: true,
          allowReactions: true,
          requireApproval: false,
          autoArchive: false,
        },
        members: users.map(u => u.id),
        admins: [currentUserId],
      }
    ];

    // Add project-specific channel if we're in a project context
    if (currentProjectId) {
      const project = projects.find(p => p.id === currentProjectId);
      if (project) {
        defaultChannels.push({
          id: `project-${currentProjectId}`,
          name: project.name.toLowerCase().replace(/\s+/g, '-'),
          description: `Project discussions for ${project.name}`,
          type: 'project',
          icon: '📁',
          color: project.color,
          projectId: currentProjectId,
          createdAt: new Date().toISOString(),
          createdBy: currentUserId,
          lastActivity: new Date().toISOString(),
          memberCount: project.teamMembers?.length || 0,
          isArchived: false,
          isMuted: false,
          settings: {
            allowFileUploads: true,
            allowThreads: true,
            allowReactions: true,
            requireApproval: false,
            autoArchive: false,
          },
          members: project.teamMembers?.map((tm: TeamMember) => tm.userId) || [],
          admins: [currentUserId],
        });
      }
    }

    // Add task-specific channel if we're in a task context
    if (currentTaskId) {
      const task = tasks.find(t => t.id === currentTaskId);
      if (task) {
        defaultChannels.push({
          id: `task-${currentTaskId}`,
          name: `task-${task.name.toLowerCase().replace(/\s+/g, '-')}`,
          description: `Task discussions for ${task.name}`,
          type: 'task',
          icon: '✅',
          color: '#f59e0b',
          taskId: currentTaskId,
          projectId: task.projectId,
          createdAt: new Date().toISOString(),
          createdBy: currentUserId,
          lastActivity: new Date().toISOString(),
          memberCount: task.assignedTo?.length || 0,
          isArchived: false,
          isMuted: false,
          settings: {
            allowFileUploads: true,
            allowThreads: true,
            allowReactions: true,
            requireApproval: false,
            autoArchive: false,
          },
          members: task.assignedTo || [],
          admins: [currentUserId],
        });
      }
    }

    setChannels(defaultChannels);
    
    // Set active channel to project/task channel if available, otherwise general
    if (currentTaskId) {
      setActiveChannelId(`task-${currentTaskId}`);
    } else if (currentProjectId) {
      setActiveChannelId(`project-${currentProjectId}`);
    } else {
      setActiveChannelId('general');
    }
  }, [currentProjectId, currentTaskId, currentUserId, users, projects, tasks]);

  const handleSendMessage = (text: string, attachments?: any[]) => {
    if (!activeChannelId || !text.trim()) return;

    const newMessage: ChatMessage = {
      id: `msg-${Date.now()}-${Math.random()}`,
      channelId: activeChannelId,
      text,
      authorId: currentUserId,
      authorName: users.find(u => u.id === currentUserId)?.name || 'Unknown User',
      authorAvatar: users.find(u => u.id === currentUserId)?.avatar,
      timestamp: new Date().toISOString(),
      isEdited: false,
      isDeleted: false,
      status: 'sending',
      attachments: attachments?.map((att, index) => ({
        id: `att-${Date.now()}-${index}`,
        type: att.type,
        name: att.name,
        url: att.url,
        size: att.size,
        mimeType: att.mimeType,
        uploadedBy: currentUserId,
        uploadedAt: new Date().toISOString(),
      })) || [],
      reactions: [],
      mentions: extractMentions(text),
    };

    // Optimistic update
    setMessages(prev => ({
      ...prev,
      [activeChannelId]: [...(prev[activeChannelId] || []), newMessage]
    }));

    // Simulate message sending
    setTimeout(() => {
      setMessages(prev => ({
        ...prev,
        [activeChannelId]: prev[activeChannelId].map(msg => 
          msg.id === newMessage.id ? { ...msg, status: 'sent' } : msg
        )
      }));
    }, 1000);
  };

  const extractMentions = (text: string): string[] => {
    const mentionRegex = /@(\w+)/g;
    const mentions: string[] = [];
    let match;
    
    while ((match = mentionRegex.exec(text)) !== null) {
      const username = match[1];
      const user = users.find(u => 
        u.name.toLowerCase().includes(username.toLowerCase()) ||
        u.email.toLowerCase().includes(username.toLowerCase())
      );
      if (user) {
        mentions.push(user.id);
      }
    }
    
    return mentions;
  };

  const handleThreadOpen = (messageId: string) => {
    setActiveThreadId(messageId);
    setShowThreadPanel(true);
  };

  const handleThreadClose = () => {
    setShowThreadPanel(false);
    setActiveThreadId('');
  };

  const handleChannelSelect = (channelId: string) => {
    setActiveChannelId(channelId);
    setShowThreadPanel(false);
    setActiveThreadId('');
    
    // Mark channel as read
    setUnreadCounts(prev => ({
      ...prev,
      [channelId]: 0
    }));
  };

  // Helper to find or create a direct channel
  const handleDirectChannelRequest = (otherUserId: string) => {
    // Check if a direct channel already exists
    let directChannel = channels.find(
      ch => ch.type === 'direct' &&
        ch.participants &&
        ch.participants.includes(currentUserId) &&
        ch.participants.includes(otherUserId) &&
        ch.participants.length === 2
    );
    if (!directChannel) {
      // Create a new direct channel
      directChannel = {
        id: `dm-${[currentUserId, otherUserId].sort().join('-')}`,
        name: '',
        type: 'direct',
        icon: '',
        color: '#6366f1',
        createdAt: new Date().toISOString(),
        createdBy: currentUserId,
        lastActivity: new Date().toISOString(),
        memberCount: 2,
        isArchived: false,
        isMuted: false,
        settings: {
          allowFileUploads: true,
          allowThreads: true,
          allowReactions: true,
          requireApproval: false,
          autoArchive: false,
        },
        members: [currentUserId, otherUserId],
        admins: [currentUserId],
        isDirectMessage: true,
        participants: [currentUserId, otherUserId],
      };
      setChannels(prev => [...prev, directChannel!]);
    }
    setActiveChannelId(directChannel.id);
    setShowThreadPanel(false);
    setActiveThreadId('');
    setUnreadCounts(prev => ({ ...prev, [directChannel!.id]: 0 }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex bg-gray-900">
      {/* Chat Sidebar */}
      <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
        <ChatSidebar
          channels={channels}
          activeChannelId={activeChannelId}
          unreadCounts={unreadCounts}
          onChannelSelect={handleChannelSelect}
          currentUserId={currentUserId}
          users={users}
          projects={projects}
          tasks={tasks}
          onDirectChannelRequest={handleDirectChannelRequest}
        />
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Message Area */}
        <div className="flex-1 flex">
          <div className="flex-1 flex flex-col">
            <ChatMessageArea
              channelId={activeChannelId}
              messages={messages[activeChannelId] || []}
              users={users}
              onThreadOpen={handleThreadOpen}
              onMessageEdit={(messageId: string, text: string) => {
                setMessages(prev => ({
                  ...prev,
                  [activeChannelId]: prev[activeChannelId].map(msg =>
                    msg.id === messageId ? { ...msg, text, isEdited: true, editedAt: new Date().toISOString() } : msg
                  )
                }));
              }}
              onMessageDelete={(messageId: string) => {
                setMessages(prev => ({
                  ...prev,
                  [activeChannelId]: prev[activeChannelId].map(msg =>
                    msg.id === messageId ? { ...msg, isDeleted: true, deletedAt: new Date().toISOString() } : msg
                  )
                }));
              }}
              onReactionAdd={(messageId: string, emoji: string) => {
                setMessages(prev => ({
                  ...prev,
                  [activeChannelId]: prev[activeChannelId].map(msg => {
                    if (msg.id === messageId) {
                      const existingReaction = msg.reactions?.find(r => r.emoji === emoji);
                      if (existingReaction) {
                        return {
                          ...msg,
                          reactions: msg.reactions?.map(r =>
                            r.emoji === emoji
                              ? { ...r, count: r.count + 1, users: [...r.users, currentUserId] }
                              : r
                          )
                        };
                      } else {
                        return {
                          ...msg,
                          reactions: [...(msg.reactions || []), {
                            emoji,
                            count: 1,
                            users: [currentUserId]
                          }]
                        };
                      }
                    }
                    return msg;
                  })
                }));
              }}
            />
            
            {/* Chat Input */}
            <ChatInput
              onSendMessage={handleSendMessage}
              channelId={activeChannelId}
              currentUserId={currentUserId}
              users={users}
            />
          </div>

          {/* Thread Panel */}
          {showThreadPanel && (
            <div className="w-96 border-l border-gray-700">
              <ChatThreadPanel
                threadId={activeThreadId}
                messages={messages[activeChannelId] || []}
                users={users}
                onClose={handleThreadClose}
                onSendReply={(text: string) => {
                  const replyMessage: ChatMessage = {
                    id: `reply-${Date.now()}-${Math.random()}`,
                    channelId: activeChannelId,
                    text,
                    authorId: currentUserId,
                    authorName: users.find(u => u.id === currentUserId)?.name || 'Unknown User',
                    authorAvatar: users.find(u => u.id === currentUserId)?.avatar,
                    timestamp: new Date().toISOString(),
                    isEdited: false,
                    isDeleted: false,
                    status: 'sending',
                    threadId: activeThreadId,
                    reactions: [],
                    mentions: extractMentions(text),
                  };

                  setMessages(prev => ({
                    ...prev,
                    [activeChannelId]: [...(prev[activeChannelId] || []), replyMessage]
                  }));
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Close Button */}
      <button
        onClick={onClose}
        className="absolute top-4 right-4 p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  );
};

export default ChatInterface; 