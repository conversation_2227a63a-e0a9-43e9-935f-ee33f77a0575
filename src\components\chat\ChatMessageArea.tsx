import React, { useRef, useEffect, useState } from 'react';
import { ChatMessage } from '../../types/chat';
import { FiMoreVertical, FiEdit, FiTrash2, FiMessageSquare, FiSmile, FiPaperclip } from 'react-icons/fi';

interface ChatMessageAreaProps {
  channelId: string;
  messages: ChatMessage[];
  users: any[];
  onThreadOpen: (messageId: string) => void;
  onMessageEdit: (messageId: string, text: string) => void;
  onMessageDelete: (messageId: string) => void;
  onReactionAdd: (messageId: string, emoji: string) => void;
}

const ChatMessageArea: React.FC<ChatMessageAreaProps> = ({
  channelId,
  messages,
  users,
  onThreadOpen,
  onMessageEdit,
  onMessageDelete,
  onReactionAdd
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editText, setEditText] = useState('');
  const [showReactionPicker, setShowReactionPicker] = useState<string | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = (now.getTime() - date.getTime()) / (1000 * 60);
    
    if (diffInMinutes < 1) return 'now';
    if (diffInMinutes < 60) return `${Math.floor(diffInMinutes)}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return date.toLocaleDateString();
  };

  const handleEditMessage = (message: ChatMessage) => {
    setEditingMessageId(message.id);
    setEditText(message.text);
  };

  const handleSaveEdit = () => {
    if (editingMessageId && editText.trim()) {
      onMessageEdit(editingMessageId, editText);
      setEditingMessageId(null);
      setEditText('');
    }
  };

  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditText('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  const commonReactions = ['👍', '❤️', '😂', '😮', '😢', '😡'];

  const renderMessageContent = (message: ChatMessage) => {
    if (message.isDeleted) {
      return (
        <div className="text-gray-500 italic">
          This message was deleted
        </div>
      );
    }

    // Process mentions
    let processedText = message.text;
    if (message.mentions && message.mentions.length > 0) {
      message.mentions.forEach(userId => {
        const user = users.find(u => u.id === userId);
        if (user) {
          const regex = new RegExp(`@${user.name}`, 'gi');
          processedText = processedText.replace(regex, `<span class="text-blue-400 font-medium">@${user.name}</span>`);
        }
      });
    }

    return (
      <div className="whitespace-pre-wrap break-words">
        <div dangerouslySetInnerHTML={{ __html: processedText }} />
      </div>
    );
  };

  const renderAttachments = (message: ChatMessage) => {
    if (!message.attachments || message.attachments.length === 0) return null;

    return (
      <div className="mt-2 space-y-2">
        {message.attachments.map(attachment => (
          <div key={attachment.id} className="border border-gray-600 rounded-lg p-3 bg-gray-800">
            {attachment.type === 'image' ? (
              <img
                src={attachment.url}
                alt={attachment.name}
                className="max-w-full max-h-64 rounded"
                loading="lazy"
              />
            ) : (
              <div className="flex items-center space-x-3">
                <FiPaperclip className="w-4 h-4 text-gray-400" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate">{attachment.name}</div>
                  {attachment.size && (
                    <div className="text-xs text-gray-400">
                      {(attachment.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                  )}
                </div>
                <a
                  href={attachment.url}
                  download={attachment.name}
                  className="text-blue-400 hover:text-blue-300 text-sm"
                >
                  Download
                </a>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderReactions = (message: ChatMessage) => {
    if (!message.reactions || message.reactions.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-1 mt-2">
        {message.reactions.map((reaction, index) => (
          <button
            key={`${reaction.emoji}-${index}`}
            onClick={() => onReactionAdd(message.id, reaction.emoji)}
            className="flex items-center space-x-1 px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors"
          >
            <span>{reaction.emoji}</span>
            <span className="text-gray-300">{reaction.count}</span>
          </button>
        ))}
      </div>
    );
  };

  const renderMessage = (message: ChatMessage, index: number) => {
    const user = users.find(u => u.id === message.authorId);
    const isCurrentUser = message.authorId === 'user1'; // Replace with actual current user ID
    const showAvatar = index === 0 || messages[index - 1]?.authorId !== message.authorId;

    return (
      <div key={message.id} className="group hover:bg-gray-800 transition-colors">
        <div className="flex space-x-3 px-4 py-2">
          {/* Avatar */}
          {showAvatar ? (
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {user?.name?.charAt(0) || 'U'}
                </span>
              </div>
            </div>
          ) : (
            <div className="flex-shrink-0 w-8" />
          )}

          {/* Message Content */}
          <div className="flex-1 min-w-0">
            {showAvatar && (
              <div className="flex items-center space-x-2 mb-1">
                <span className="text-sm font-medium text-gray-200">
                  {user?.name || 'Unknown User'}
                </span>
                <span className="text-xs text-gray-400">
                  {formatTimestamp(message.timestamp)}
                </span>
                {message.isEdited && (
                  <span className="text-xs text-gray-400">(edited)</span>
                )}
              </div>
            )}

            {/* Message Text Bubble */}
            <div className="inline-block bg-gray-700 rounded-2xl px-4 py-2 text-gray-100 shadow-sm max-w-2xl">
              {editingMessageId === message.id ? (
                <div className="mt-1">
                  <textarea
                    value={editText}
                    onChange={(e) => setEditText(e.target.value)}
                    onKeyDown={handleKeyPress}
                    className="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    autoFocus
                  />
                  <div className="flex space-x-2 mt-2">
                    <button
                      onClick={handleSaveEdit}
                      className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                    >
                      Save
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      className="px-3 py-1 bg-gray-600 text-gray-200 rounded text-sm hover:bg-gray-700"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-gray-200">
                  {renderMessageContent(message)}
                  {renderAttachments(message)}
                  {renderReactions(message)}
                </div>
              )}
            </div>

            {/* Message Actions */}
            {!message.isDeleted && !editingMessageId && (
              <div className="flex items-center space-x-2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={() => onReactionAdd(message.id, '👍')}
                  className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                >
                  <FiSmile className="w-4 h-4" />
                </button>
                <button
                  onClick={() => onThreadOpen(message.id)}
                  className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                >
                  <FiMessageSquare className="w-4 h-4" />
                </button>
                {isCurrentUser && (
                  <>
                    <button
                      onClick={() => handleEditMessage(message)}
                      className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                    >
                      <FiEdit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => onMessageDelete(message.id)}
                      className="p-1 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded"
                    >
                      <FiTrash2 className="w-4 h-4" />
                    </button>
                  </>
                )}
                <button className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded">
                  <FiMoreVertical className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex-1 flex flex-col bg-gray-900">
      {/* Channel Header */}
      <div className="px-4 py-3 border-b border-gray-700 bg-gray-800">
        <div className="flex items-center space-x-3">
          <span className="text-lg">💬</span>
          <div>
            <h3 className="text-lg font-semibold text-gray-200">
              {channelId === 'general' ? 'general' : channelId}
            </h3>
            <p className="text-sm text-gray-400">
              {messages.length} messages
            </p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-400">
            <div className="text-center">
              <div className="text-6xl mb-4">💬</div>
              <h3 className="text-lg font-medium mb-2">No messages yet</h3>
              <p className="text-sm">Start the conversation!</p>
            </div>
          </div>
        ) : (
          <div className="py-4">
            {messages.map((message, index) => renderMessage(message, index))}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessageArea; 