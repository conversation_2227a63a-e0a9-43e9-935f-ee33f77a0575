import React, { useState } from 'react';
import { ChatChannel } from '../../types/chat';
import { <PERSON>H<PERSON>, <PERSON>Lock, FiPlus, FiMoreVertical, FiSearch, FiUser, FiUsers } from 'react-icons/fi';

interface ChatSidebarProps {
  channels: ChatChannel[];
  activeChannelId: string;
  unreadCounts: { [channelId: string]: number };
  onChannelSelect: (channelId: string) => void;
  currentUserId: string;
  users: any[];
  projects: any[];
  tasks: any[];
  onDirectChannelRequest?: (userId: string) => void;
}

const ChatSidebar: React.FC<ChatSidebarProps> = ({
  channels,
  activeChannelId,
  unreadCounts,
  onChannelSelect,
  currentUserId,
  users,
  projects,
  tasks,
  onDirectChannelRequest
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateChannel, setShowCreateChannel] = useState(false);

  const publicChannels = channels.filter(ch => ch.type === 'public');
  const privateChannels = channels.filter(ch => ch.type === 'private');
  const projectChannels = channels.filter(ch => ch.type === 'project');
  const taskChannels = channels.filter(ch => ch.type === 'task');
  const directMessages = channels.filter(ch => ch.type === 'direct');

  const filteredChannels = channels.filter(channel =>
    channel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    channel.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getChannelIcon = (channel: ChatChannel) => {
    if (channel.icon) return <span className="text-lg">{channel.icon}</span>;
    
    switch (channel.type) {
      case 'public':
        return <FiHash className="w-4 h-4" />;
      case 'private':
        return <FiLock className="w-4 h-4" />;
      case 'project':
        return <FiUsers className="w-4 h-4" />;
      case 'task':
        return <FiUser className="w-4 h-4" />;
      case 'direct':
        return <FiUser className="w-4 h-4" />;
      default:
        return <FiHash className="w-4 h-4" />;
    }
  };

  const getChannelDisplayName = (channel: ChatChannel) => {
    if (channel.type === 'direct' && channel.participants) {
      const otherParticipant = channel.participants.find(p => p !== currentUserId);
      const user = users.find(u => u.id === otherParticipant);
      return user?.name || channel.name;
    }
    return channel.name;
  };

  const formatLastActivity = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) return 'now';
    if (diffInHours < 24) return `${Math.floor(diffInHours)}h`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d`;
    return date.toLocaleDateString();
  };

  return (
    <div className="flex flex-col h-full bg-gray-800 text-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Chat</h2>
          <button
            onClick={() => setShowCreateChannel(true)}
            className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
          >
            <FiPlus className="w-4 h-4" />
          </button>
        </div>
        
        {/* Search */}
        <div className="mt-3 relative">
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search channels..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-9 pr-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Channels List */}
      <div className="flex-1 overflow-y-auto">
        {/* Public Channels */}
        {publicChannels.length > 0 && (
          <div className="mb-4">
            <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Public Channels
            </div>
            {publicChannels.map(channel => (
              <ChannelItem
                key={channel.id}
                channel={channel}
                isActive={activeChannelId === channel.id}
                unreadCount={unreadCounts[channel.id] || 0}
                onSelect={() => onChannelSelect(channel.id)}
                getIcon={getChannelIcon}
                getDisplayName={getChannelDisplayName}
              />
            ))}
          </div>
        )}

        {/* Private Channels */}
        {privateChannels.length > 0 && (
          <div className="mb-4">
            <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Private Channels
            </div>
            {privateChannels.map(channel => (
              <ChannelItem
                key={channel.id}
                channel={channel}
                isActive={activeChannelId === channel.id}
                unreadCount={unreadCounts[channel.id] || 0}
                onSelect={() => onChannelSelect(channel.id)}
                getIcon={getChannelIcon}
                getDisplayName={getChannelDisplayName}
              />
            ))}
          </div>
        )}

        {/* Project Channels */}
        {projectChannels.length > 0 && (
          <div className="mb-4">
            <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Project Channels
            </div>
            {projectChannels.map(channel => (
              <ChannelItem
                key={channel.id}
                channel={channel}
                isActive={activeChannelId === channel.id}
                unreadCount={unreadCounts[channel.id] || 0}
                onSelect={() => onChannelSelect(channel.id)}
                getIcon={getChannelIcon}
                getDisplayName={getChannelDisplayName}
              />
            ))}
          </div>
        )}

        {/* Task Channels */}
        {taskChannels.length > 0 && (
          <div className="mb-4">
            <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Task Channels
            </div>
            {taskChannels.map(channel => (
              <ChannelItem
                key={channel.id}
                channel={channel}
                isActive={activeChannelId === channel.id}
                unreadCount={unreadCounts[channel.id] || 0}
                onSelect={() => onChannelSelect(channel.id)}
                getIcon={getChannelIcon}
                getDisplayName={getChannelDisplayName}
              />
            ))}
          </div>
        )}

        {/* Direct Messages */}
        {directMessages.length > 0 && (
          <div className="mb-4">
            <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Direct Messages
            </div>
            {directMessages.map(channel => (
              <ChannelItem
                key={channel.id}
                channel={channel}
                isActive={activeChannelId === channel.id}
                unreadCount={unreadCounts[channel.id] || 0}
                onSelect={() => onChannelSelect(channel.id)}
                getIcon={getChannelIcon}
                getDisplayName={getChannelDisplayName}
              />
            ))}
          </div>
        )}

        {/* All Users List */}
        <div className="mt-2">
          <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">All Users</div>
          <div className="space-y-2 px-4 max-h-40 overflow-y-auto">
            {users.map((user: any) => {
              if (user.id === currentUserId) return null;
              return (
                <button
                  key={user.id}
                  className="flex items-center space-x-3 w-full hover:bg-gray-700 rounded px-2 py-1 transition-colors"
                  onClick={() => {
                    if (onDirectChannelRequest) {
                      onDirectChannelRequest(user.id);
                    }
                  }}
                >
                  <div className="w-7 h-7 bg-gray-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-medium">{user.name?.charAt(0) || 'U'}</span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm truncate">{user.name}</div>
                    <div className="text-xs text-gray-400">{user.isOnline ? 'Online' : 'Offline'}</div>
                  </div>
                  <span className={`w-2 h-2 rounded-full ${user.isOnline ? 'bg-green-400' : 'bg-gray-500'}`}></span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* User Status */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">
              {users.find(u => u.id === currentUserId)?.name?.charAt(0) || 'U'}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium truncate">
              {users.find(u => u.id === currentUserId)?.name || 'Unknown User'}
            </div>
            <div className="text-xs text-gray-400">Online</div>
          </div>
          <button className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded">
            <FiMoreVertical className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

// Channel Item Component
interface ChannelItemProps {
  channel: ChatChannel;
  isActive: boolean;
  unreadCount: number;
  onSelect: () => void;
  getIcon: (channel: ChatChannel) => React.ReactNode;
  getDisplayName: (channel: ChatChannel) => string;
}

const ChannelItem: React.FC<ChannelItemProps> = ({
  channel,
  isActive,
  unreadCount,
  onSelect,
  getIcon,
  getDisplayName
}) => {
  return (
    <button
      onClick={onSelect}
      className={`w-full px-4 py-2 text-left hover:bg-gray-700 transition-colors ${
        isActive ? 'bg-gray-700 text-white' : 'text-gray-300'
      }`}
    >
      <div className="flex items-center space-x-3">
        <div className="flex-shrink-0">
          {getIcon(channel)}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium truncate">
              {getDisplayName(channel)}
            </span>
            {unreadCount > 0 && (
              <span className="ml-2 px-2 py-1 text-xs font-medium bg-red-500 text-white rounded-full">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </div>
          {channel.description && (
            <div className="text-xs text-gray-400 truncate">
              {channel.description}
            </div>
          )}
        </div>
      </div>
    </button>
  );
};

export default ChatSidebar; 