import React, { useState, useRef, useEffect } from 'react';
import { ChatMessage } from '../../types/chat';
import { FiX, FiSend, FiSmile } from 'react-icons/fi';

interface ChatThreadPanelProps {
  threadId: string;
  messages: ChatMessage[];
  users: any[];
  onClose: () => void;
  onSendReply: (text: string) => void;
}

const ChatThreadPanel: React.FC<ChatThreadPanelProps> = ({
  threadId,
  messages,
  users,
  onClose,
  onSendReply
}) => {
  const [replyText, setReplyText] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Find the parent message and all replies
  const parentMessage = messages.find(msg => msg.id === threadId);
  const threadReplies = messages.filter(msg => msg.threadId === threadId);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [threadReplies]);

  const handleSendReply = () => {
    if (replyText.trim()) {
      onSendReply(replyText);
      setReplyText('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendReply();
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = (now.getTime() - date.getTime()) / (1000 * 60);
    
    if (diffInMinutes < 1) return 'now';
    if (diffInMinutes < 60) return `${Math.floor(diffInMinutes)}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return date.toLocaleDateString();
  };

  const renderMessage = (message: ChatMessage) => {
    const user = users.find(u => u.id === message.authorId);

    return (
      <div key={message.id} className="flex space-x-3 p-3 hover:bg-gray-800 transition-colors">
        {/* Avatar */}
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">
              {user?.name?.charAt(0) || 'U'}
            </span>
          </div>
        </div>

        {/* Message Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <span className="text-sm font-medium text-gray-200">
              {user?.name || 'Unknown User'}
            </span>
            <span className="text-xs text-gray-400">
              {formatTimestamp(message.timestamp)}
            </span>
            {message.isEdited && (
              <span className="text-xs text-gray-400">(edited)</span>
            )}
          </div>
          <div className="text-gray-200 whitespace-pre-wrap break-words">
            {message.text}
          </div>
        </div>
      </div>
    );
  };

  if (!parentMessage) {
    return (
      <div className="h-full bg-gray-800 border-l border-gray-700 flex flex-col">
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-200">Thread</h3>
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
            >
              <FiX className="w-5 h-5" />
            </button>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center text-gray-400">
          <div className="text-center">
            <div className="text-4xl mb-2">💬</div>
            <p>Thread not found</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-800 border-l border-gray-700 flex flex-col">
      {/* Thread Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="text-lg">💬</span>
            <div>
              <h3 className="text-lg font-semibold text-gray-200">Thread</h3>
              <p className="text-sm text-gray-400">
                {threadReplies.length} replies
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Parent Message */}
      <div className="p-4 border-b border-gray-700 bg-gray-900">
        <div className="text-sm text-gray-400 mb-2">Original message</div>
        {renderMessage(parentMessage)}
      </div>

      {/* Thread Replies */}
      <div className="flex-1 overflow-y-auto">
        {threadReplies.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-400">
            <div className="text-center">
              <div className="text-4xl mb-2">💬</div>
              <p className="text-sm">No replies yet</p>
              <p className="text-xs">Be the first to reply!</p>
            </div>
          </div>
        ) : (
          <div className="py-2">
            {threadReplies.map(renderMessage)}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Reply Input */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-end space-x-3">
          <div className="flex-1">
            <textarea
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Reply to thread..."
              className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-gray-200 placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={2}
            />
          </div>
          <button
            onClick={handleSendReply}
            disabled={!replyText.trim()}
            className="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
          >
            <FiSend className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatThreadPanel; 