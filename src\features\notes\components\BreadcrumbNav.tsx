import React from 'react';
import type { Note } from '../types';

interface BreadcrumbNavProps {
  ancestors: Note[];
  onCrumbClick: (id: string) => void;
}

const BreadcrumbNav: React.FC<BreadcrumbNavProps> = ({ ancestors, onCrumbClick }) => {
  if (ancestors.length === 0) {
    return null;
  }

  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-400">
      {ancestors.map((ancestor, index) => (
        <React.Fragment key={ancestor.id}>
          <button
            onClick={() => onCrumbClick(ancestor.id)}
            className="hover:text-blue-400 transition-colors duration-200 truncate max-w-[120px]"
            title={ancestor.content || 'Untitled'}
          >
            {ancestor.content || 'Untitled'}
          </button>
          {index < ancestors.length - 1 && (
            <svg className="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
};

export default BreadcrumbNav; 