import React, { useState, useMemo } from 'react';
import { FiSearch, FiFilter, FiX } from 'react-icons/fi';
import type { Note } from '../types';

interface NotesFilterProps {
  notes: Record<string, Note>;
  onFilteredNotesChange: (filteredNotes: string[]) => void;
  availableTags: string[];
}

const NotesFilter: React.FC<NotesFilterProps> = ({
  notes,
  onFilteredNotesChange,
  availableTags
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showTagFilter, setShowTagFilter] = useState(false);

  // Filter notes based on search term and selected tags
  const filteredNoteIds = useMemo(() => {
    const noteIds = Object.keys(notes);
    
    return noteIds.filter(noteId => {
      const note = notes[noteId];
      
      // Search term filter
      const matchesSearch = !searchTerm || 
        note.content.toLowerCase().includes(searchTerm.toLowerCase());
      
      // Tag filter
      const matchesTags = selectedTags.length === 0 || 
        selectedTags.every(tag => note.tags.includes(tag));
      
      return matchesSearch && matchesTags;
    });
  }, [notes, searchTerm, selectedTags]);

  // Update parent component when filtered notes change
  React.useEffect(() => {
    onFilteredNotesChange(filteredNoteIds);
  }, [filteredNoteIds, onFilteredNotesChange]);

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedTags([]);
  };

  const hasActiveFilters = searchTerm || selectedTags.length > 0;

  return (
    <div className="mb-6 space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
        <input
          type="text"
          placeholder="Search notes..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 outline-none"
        />
        {searchTerm && (
          <button
            onClick={() => setSearchTerm('')}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
          >
            <FiX size={16} />
          </button>
        )}
      </div>

      {/* Filter Controls */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => setShowTagFilter(!showTagFilter)}
          className={`flex items-center gap-2 px-3 py-1.5 rounded-lg border transition-colors ${
            showTagFilter || selectedTags.length > 0
              ? 'bg-blue-600 border-blue-500 text-white'
              : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
          }`}
        >
          <FiFilter size={14} />
          Tags
          {selectedTags.length > 0 && (
            <span className="bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full">
              {selectedTags.length}
            </span>
          )}
        </button>

        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-sm text-gray-400 hover:text-white"
          >
            Clear filters
          </button>
        )}

        <div className="text-sm text-gray-400">
          {filteredNoteIds.length} of {Object.keys(notes).length} notes
        </div>
      </div>

      {/* Tag Filter */}
      {showTagFilter && (
        <div className="bg-gray-700 rounded-lg p-4 border border-gray-600">
          <div className="flex flex-wrap gap-2">
            {availableTags.map(tag => (
              <button
                key={tag}
                onClick={() => handleTagToggle(tag)}
                className={`px-3 py-1.5 rounded-full text-sm transition-colors ${
                  selectedTags.includes(tag)
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                }`}
              >
                {tag}
              </button>
            ))}
            {availableTags.length === 0 && (
              <div className="text-gray-400 text-sm">No tags available</div>
            )}
          </div>
        </div>
      )}

      {/* Selected Tags Display */}
      {selectedTags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedTags.map(tag => (
            <span
              key={tag}
              className="inline-flex items-center gap-1 px-2 py-1 bg-blue-600 text-white text-xs rounded-full"
            >
              {tag}
              <button
                onClick={() => handleTagToggle(tag)}
                className="hover:bg-blue-700 rounded-full p-0.5"
              >
                <FiX size={10} />
              </button>
            </span>
          ))}
        </div>
      )}
    </div>
  );
};

export default NotesFilter;
