import React, { useState, useRef, useEffect } from 'react';

interface SimpleNoteEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

const SimpleNoteEditor: React.FC<SimpleNoteEditorProps> = ({
  content,
  onChange,
  placeholder = "Enter your notes here...",
  className = ""
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  };

  useEffect(() => {
    if (isEditing) {
      adjustTextareaHeight();
    }
  }, [content, isEditing]);

  // Format content for display (convert bullet points)
  const formatContentForDisplay = (text: string) => {
    if (!text) return [];
    
    const lines = text.split('\n').filter(line => line.trim());
    return lines.map((line, index) => {
      const trimmedLine = line.trim();
      
      // Check if line starts with bullet point markers
      const isBullet = trimmedLine.startsWith('•') || trimmedLine.startsWith('-') || trimmedLine.startsWith('*');
      const displayContent = isBullet ? trimmedLine.substring(1).trim() : trimmedLine;
      
      return (
        <div key={index} className="flex items-start mb-2">
          <div className="flex-shrink-0 w-2 h-2 bg-gray-400 rounded-full mr-3 mt-2"></div>
          <span className="text-gray-100 text-base leading-relaxed">{displayContent}</span>
        </div>
      );
    });
  };

  const handleClick = () => {
    setIsEditing(true);
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(textareaRef.current.value.length, textareaRef.current.value.length);
      }
    }, 0);
  };

  const handleBlur = () => {
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Escape') {
      setIsEditing(false);
      textareaRef.current?.blur();
    }
  };

  const bulletPoints = formatContentForDisplay(content);

  if (isEditing) {
    return (
      <div className={`w-full ${className}`}>
        <textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => onChange(e.target.value)}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          onInput={adjustTextareaHeight}
          placeholder={placeholder}
          className="w-full bg-[#23272f] text-gray-100 border border-gray-600 rounded-lg p-4 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          style={{ minHeight: '120px' }}
        />
        <div className="text-xs text-gray-400 mt-2">
          Tip: Use • or - at the start of lines for bullet points. Press Escape to finish editing.
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full cursor-pointer ${className}`}
      onClick={handleClick}
    >
      {bulletPoints.length > 0 ? (
        <div className="space-y-1 p-4 bg-[#23272f] rounded-lg border border-gray-600 hover:border-blue-500 transition-colors">
          {bulletPoints}
        </div>
      ) : (
        <div className="p-4 bg-[#23272f] rounded-lg border border-gray-600 hover:border-blue-500 transition-colors">
          <div className="text-gray-400 italic">{placeholder}</div>
        </div>
      )}
      <div className="text-xs text-gray-400 mt-2">
        Click to edit
      </div>
    </div>
  );
};

export default SimpleNoteEditor;
