import React, { useState, useRef, useEffect } from 'react';
import { FiBold, FiItalic, FiUnderline, FiCode, FiList } from 'react-icons/fi';

interface SimpleNoteEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

const SimpleNoteEditor: React.FC<SimpleNoteEditorProps> = ({
  content,
  onChange,
  placeholder = "Enter your notes here...",
  className = ""
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  };

  useEffect(() => {
    if (isEditing) {
      adjustTextareaHeight();
    }
  }, [content, isEditing]);

  // Format text with markdown-style formatting
  const formatText = (text: string) => {
    const parts = [];
    let currentIndex = 0;
    let partKey = 0;

    // Regex patterns for different formatting
    const patterns = [
      { regex: /\*\*(.*?)\*\*/g, component: 'strong', className: 'font-bold' },
      { regex: /\*(.*?)\*/g, component: 'em', className: 'italic' },
      { regex: /__(.*?)__/g, component: 'span', className: 'underline' },
      { regex: /`(.*?)`/g, component: 'code', className: 'bg-gray-700 px-1 rounded text-sm font-mono' },
    ];

    let workingText = text;
    const replacements: Array<{ start: number; end: number; element: JSX.Element }> = [];

    // Find all matches for all patterns
    patterns.forEach(({ regex, component, className }) => {
      let match;
      regex.lastIndex = 0; // Reset regex
      while ((match = regex.exec(text)) !== null) {
        const element = React.createElement(
          component,
          { key: `format-${partKey++}`, className },
          match[1]
        );
        replacements.push({
          start: match.index,
          end: match.index + match[0].length,
          element
        });
      }
    });

    // Sort replacements by start position
    replacements.sort((a, b) => a.start - b.start);

    // Build the final array with formatted elements
    let lastEnd = 0;
    replacements.forEach(({ start, end, element }) => {
      // Add text before this replacement
      if (start > lastEnd) {
        parts.push(text.substring(lastEnd, start));
      }
      // Add the formatted element
      parts.push(element);
      lastEnd = end;
    });

    // Add remaining text
    if (lastEnd < text.length) {
      parts.push(text.substring(lastEnd));
    }

    return parts.length > 0 ? parts : [text];
  };

  // Format content for display (convert bullet points)
  const formatContentForDisplay = (text: string) => {
    if (!text) return [];

    const lines = text.split('\n').filter(line => line.trim());
    return lines.map((line, index) => {
      const trimmedLine = line.trim();

      // Calculate indentation level (2 spaces = 1 level)
      const indentMatch = line.match(/^(\s*)/);
      const indentLevel = indentMatch ? Math.floor(indentMatch[1].length / 2) : 0;
      const marginLeft = indentLevel * 20; // 20px per indent level

      // Check if line starts with bullet point markers
      const isBullet = trimmedLine.startsWith('•') || trimmedLine.startsWith('-') || trimmedLine.startsWith('*');
      const displayContent = isBullet ? trimmedLine.substring(1).trim() : trimmedLine;

      // Format the content with markdown-style formatting
      const formattedContent = formatText(displayContent);

      return (
        <div key={index} className="flex items-start mb-2" style={{ marginLeft: `${marginLeft}px` }}>
          <div className="flex-shrink-0 w-2 h-2 bg-gray-400 rounded-full mr-3 mt-2"></div>
          <span className="text-gray-100 text-base leading-relaxed">{formattedContent}</span>
        </div>
      );
    });
  };

  // Ensure content has bullet points for editing
  const ensureBulletPoints = (text: string) => {
    if (!text) return '';

    const lines = text.split('\n');
    return lines.map(line => {
      const trimmedLine = line.trim();
      if (!trimmedLine) return '';

      // If line doesn't start with bullet point markers, add one
      const hasBullet = trimmedLine.startsWith('•') || trimmedLine.startsWith('-') || trimmedLine.startsWith('*');
      return hasBullet ? line : `• ${trimmedLine}`;
    }).join('\n');
  };

  // Text formatting functions
  const applyFormatting = (formatType: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);

    let formattedText = '';
    let cursorOffset = 0;

    switch (formatType) {
      case 'bold':
        formattedText = `**${selectedText}**`;
        cursorOffset = selectedText ? 0 : 2; // If no selection, place cursor between **
        break;
      case 'italic':
        formattedText = `*${selectedText}*`;
        cursorOffset = selectedText ? 0 : 1; // If no selection, place cursor between *
        break;
      case 'underline':
        formattedText = `__${selectedText}__`;
        cursorOffset = selectedText ? 0 : 2; // If no selection, place cursor between __
        break;
      case 'code':
        formattedText = `\`${selectedText}\``;
        cursorOffset = selectedText ? 0 : 1; // If no selection, place cursor between `
        break;
      default:
        return;
    }

    const newContent = content.substring(0, start) + formattedText + content.substring(end);
    onChange(newContent);

    // Set cursor position
    setTimeout(() => {
      if (textarea) {
        const newCursorPos = selectedText ? end + formattedText.length - selectedText.length : start + formattedText.length - cursorOffset;
        textarea.setSelectionRange(newCursorPos, newCursorPos);
        textarea.focus();
      }
    }, 0);
  };

  const handleClick = () => {
    setIsEditing(true);
    // Ensure content has bullet points when switching to edit mode
    const contentWithBullets = ensureBulletPoints(content);
    if (contentWithBullets !== content) {
      onChange(contentWithBullets);
    }
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(textareaRef.current.value.length, textareaRef.current.value.length);
      }
    }, 0);
  };

  const handleBlur = () => {
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Handle keyboard shortcuts for formatting
    if (e.ctrlKey || e.metaKey) {
      switch (e.key.toLowerCase()) {
        case 'b':
          e.preventDefault();
          applyFormatting('bold');
          return;
        case 'i':
          e.preventDefault();
          applyFormatting('italic');
          return;
        case 'u':
          e.preventDefault();
          applyFormatting('underline');
          return;
        case '`':
          e.preventDefault();
          applyFormatting('code');
          return;
      }
    }

    if (e.key === 'Escape') {
      setIsEditing(false);
      textareaRef.current?.blur();
    } else if (e.key === 'Enter') {
      e.preventDefault();

      const textarea = textareaRef.current;
      if (!textarea) return;

      const cursorPosition = textarea.selectionStart;
      const textBeforeCursor = content.substring(0, cursorPosition);
      const textAfterCursor = content.substring(cursorPosition);

      // Find current line to get its indentation
      const lines = textBeforeCursor.split('\n');
      const currentLine = lines[lines.length - 1];
      const indentMatch = currentLine.match(/^(\s*)/);
      const currentIndent = indentMatch ? indentMatch[1] : '';

      // Add new line with bullet point and same indentation
      const newContent = textBeforeCursor + '\n' + currentIndent + '• ' + textAfterCursor;
      onChange(newContent);

      // Set cursor position after the bullet point
      setTimeout(() => {
        if (textarea) {
          const newCursorPosition = cursorPosition + currentIndent.length + 3; // +length for indent + 3 for '\n• '
          textarea.setSelectionRange(newCursorPosition, newCursorPosition);
          adjustTextareaHeight();
        }
      }, 0);
    } else if (e.key === 'Tab') {
      e.preventDefault();

      const textarea = textareaRef.current;
      if (!textarea) return;

      const cursorPosition = textarea.selectionStart;
      const textBeforeCursor = content.substring(0, cursorPosition);
      const textAfterCursor = content.substring(cursorPosition);

      // Find the start of the current line
      const lines = textBeforeCursor.split('\n');
      const currentLineIndex = lines.length - 1;
      const currentLine = lines[currentLineIndex];

      if (e.shiftKey) {
        // Shift+Tab: Outdent (remove indentation)
        const outdentedLine = currentLine.replace(/^  /, ''); // Remove 2 spaces
        if (outdentedLine !== currentLine) {
          lines[currentLineIndex] = outdentedLine;
          const newContent = lines.join('\n') + textAfterCursor;
          onChange(newContent);

          // Adjust cursor position
          setTimeout(() => {
            if (textarea) {
              const newCursorPosition = cursorPosition - 2; // -2 for removed spaces
              textarea.setSelectionRange(Math.max(0, newCursorPosition), Math.max(0, newCursorPosition));
            }
          }, 0);
        }
      } else {
        // Tab: Indent (add indentation)
        const indentedLine = '  ' + currentLine; // Add 2 spaces
        lines[currentLineIndex] = indentedLine;
        const newContent = lines.join('\n') + textAfterCursor;
        onChange(newContent);

        // Adjust cursor position
        setTimeout(() => {
          if (textarea) {
            const newCursorPosition = cursorPosition + 2; // +2 for added spaces
            textarea.setSelectionRange(newCursorPosition, newCursorPosition);
          }
        }, 0);
      }
    }
  };

  const bulletPoints = formatContentForDisplay(content);

  if (isEditing) {
    return (
      <div className={`w-full ${className}`}>
        {/* Formatting Toolbar */}
        <div className="flex items-center gap-2 mb-3 p-2 bg-[#1a1e26] border border-gray-600 rounded-lg">
          <button
            onClick={() => applyFormatting('bold')}
            className="p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded transition-colors"
            title="Bold"
          >
            <FiBold size={16} />
          </button>
          <button
            onClick={() => applyFormatting('italic')}
            className="p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded transition-colors"
            title="Italic"
          >
            <FiItalic size={16} />
          </button>
          <button
            onClick={() => applyFormatting('underline')}
            className="p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded transition-colors"
            title="Underline"
          >
            <FiUnderline size={16} />
          </button>
          <button
            onClick={() => applyFormatting('code')}
            className="p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded transition-colors"
            title="Code"
          >
            <FiCode size={16} />
          </button>
          <div className="w-px h-6 bg-gray-600 mx-1"></div>
          <span className="text-xs text-gray-400">
            Tab: indent • Shift+Tab: outdent • Esc: finish
          </span>
        </div>

        <textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => onChange(e.target.value)}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          onInput={adjustTextareaHeight}
          placeholder={placeholder}
          className="w-full bg-[#23272f] text-gray-100 border border-gray-600 rounded-lg p-4 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
          style={{ minHeight: '120px' }}
        />
        <div className="text-xs text-gray-400 mt-2">
          Tip: Use • or - at the start of lines for bullet points. Press Escape to finish editing.
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`w-full cursor-pointer ${className}`}
      onClick={handleClick}
    >
      {bulletPoints.length > 0 ? (
        <div className="space-y-1 p-4 bg-[#23272f] rounded-lg border border-gray-600 hover:border-blue-500 transition-colors">
          {bulletPoints}
        </div>
      ) : (
        <div className="p-4 bg-[#23272f] rounded-lg border border-gray-600 hover:border-blue-500 transition-colors">
          <div className="text-gray-400 italic">{placeholder}</div>
        </div>
      )}
      <div className="text-xs text-gray-400 mt-2">
        Click to edit
      </div>
    </div>
  );
};

export default SimpleNoteEditor;
