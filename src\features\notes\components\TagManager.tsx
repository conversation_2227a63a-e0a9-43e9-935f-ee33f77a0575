import React, { useState, useRef, useEffect } from 'react';
import { FiTag, FiX, FiPlus } from 'react-icons/fi';

interface TagManagerProps {
  tags: string[];
  onTagsChange: (tags: string[]) => void;
  availableTags?: string[];
  className?: string;
}

const TagManager: React.FC<TagManagerProps> = ({
  tags,
  onTagsChange,
  availableTags = [],
  className = ''
}) => {
  const [isAdding, setIsAdding] = useState(false);
  const [newTag, setNewTag] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isAdding && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isAdding]);

  useEffect(() => {
    if (newTag.trim()) {
      const filtered = availableTags.filter(tag => 
        tag.toLowerCase().includes(newTag.toLowerCase()) && 
        !tags.includes(tag)
      );
      setSuggestions(filtered.slice(0, 5));
    } else {
      setSuggestions([]);
    }
  }, [newTag, availableTags, tags]);

  const handleAddTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      onTagsChange([...tags, trimmedTag]);
    }
    setNewTag('');
    setIsAdding(false);
    setSuggestions([]);
  };

  const handleRemoveTag = (tagToRemove: string) => {
    onTagsChange(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (suggestions.length > 0) {
        handleAddTag(suggestions[0]);
      } else {
        handleAddTag(newTag);
      }
    } else if (e.key === 'Escape') {
      setIsAdding(false);
      setNewTag('');
      setSuggestions([]);
    } else if (e.key === 'ArrowDown' && suggestions.length > 0) {
      e.preventDefault();
      // Focus first suggestion (could be enhanced with keyboard navigation)
    }
  };

  const handleBlur = () => {
    // Delay to allow clicking on suggestions
    setTimeout(() => {
      setIsAdding(false);
      setNewTag('');
      setSuggestions([]);
    }, 150);
  };

  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      <FiTag className="text-gray-400" size={16} />
      
      {tags.map(tag => (
        <span
          key={tag}
          className="inline-flex items-center gap-1 px-2 py-1 bg-blue-600 text-white text-xs rounded-full"
        >
          {tag}
          <button
            onClick={() => handleRemoveTag(tag)}
            className="hover:bg-blue-700 rounded-full p-0.5"
          >
            <FiX size={12} />
          </button>
        </span>
      ))}

      {isAdding ? (
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            placeholder="Add tag..."
            className="px-2 py-1 bg-gray-700 text-white text-xs rounded border border-gray-600 focus:border-blue-500 outline-none min-w-[80px]"
          />
          
          {suggestions.length > 0 && (
            <div className="absolute top-full left-0 mt-1 bg-[#1a1d23] border border-gray-600 rounded shadow-lg z-50 min-w-[120px]">
              {suggestions.map(suggestion => (
                <button
                  key={suggestion}
                  onClick={() => handleAddTag(suggestion)}
                  className="block w-full text-left px-3 py-2 text-sm text-gray-300 hover:bg-gray-700 first:rounded-t last:rounded-b"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          )}
        </div>
      ) : (
        <button
          onClick={() => setIsAdding(true)}
          className="inline-flex items-center gap-1 px-2 py-1 text-xs text-gray-400 hover:text-gray-300 border border-dashed border-gray-600 hover:border-gray-500 rounded transition-colors"
        >
          <FiPlus size={12} />
          Add tag
        </button>
      )}
    </div>
  );
};

export default TagManager;
