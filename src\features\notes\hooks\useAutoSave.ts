import { useEffect, useRef } from 'react';

export const useAutoSave = <T>(
  value: T,
  onSave: (value: T) => void,
  delay: number = 1000
) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastSavedValue = useRef<T>(value);

  useEffect(() => {
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Only save if value has actually changed
    if (JSON.stringify(value) !== JSON.stringify(lastSavedValue.current)) {
      timeoutRef.current = setTimeout(() => {
        onSave(value);
        lastSavedValue.current = value;
      }, delay);
    }

    // Cleanup on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, onSave, delay]);

  // Force save immediately
  const forceSave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    onSave(value);
    lastSavedValue.current = value;
  };

  return { forceSave };
}; 