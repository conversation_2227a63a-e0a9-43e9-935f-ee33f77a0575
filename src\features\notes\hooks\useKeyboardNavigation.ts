import { useCallback } from 'react';
import { getNextSibling, getPreviousSibling } from '../utils/treeHelpers';
import type { Note } from '../types';

export const useKeyboardNavigation = (
  notes: Record<string, Note>,
  focusedId: string | null,
  setFocusedId: (id: string | null) => void
) => {
  const navigateToNext = useCallback(() => {
    if (!focusedId) return;
    const next = getNextSibling(focusedId, notes);
    if (next) {
      setFocusedId(next);
    }
  }, [focusedId, notes, setFocusedId]);

  const navigateToPrevious = useCallback(() => {
    if (!focusedId) return;
    const previous = getPreviousSibling(focusedId, notes);
    if (previous) {
      setFocusedId(previous);
    }
  }, [focusedId, notes, setFocusedId]);

  const navigateToParent = useCallback(() => {
    if (!focusedId) return;
    const note = notes[focusedId];
    if (note && note.parentId) {
      setFocusedId(note.parentId);
    }
  }, [focusedId, notes, setFocusedId]);

  const navigateToFirstChild = useCallback(() => {
    if (!focusedId) return;
    const note = notes[focusedId];
    if (note && note.children.length > 0) {
      setFocusedId(note.children[0]);
    }
  }, [focusedId, notes, setFocusedId]);

  return {
    navigateToNext,
    navigateToPrevious,
    navigateToParent,
    navigateToFirstChild,
  };
}; 