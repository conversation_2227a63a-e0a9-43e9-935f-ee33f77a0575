import React, { useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { NotesContext } from '../context/NotesContext';
import BreadcrumbNav from '../components/BreadcrumbNav';
import SimpleNoteEditor from '../components/SimpleNoteEditor';
import TagManager from '../components/TagManager';

const SimpleNoteDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const context = useContext(NotesContext);

  if (!context) {
    throw new Error('SimpleNoteDetailPage must be used within NotesProvider');
  }

  const { state, dispatch } = context;
  const note = id ? state.notes[id] : null;

  if (!note) {
    return (
      <div className="min-h-screen bg-[#1a1d23] text-gray-100 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">Note not found</div>
            <button
              onClick={() => navigate('/notes')}
              className="text-blue-400 hover:text-blue-300 underline"
            >
              Back to Notes
            </button>
          </div>
        </div>
      </div>
    );
  }

  const handleContentChange = (content: string) => {
    dispatch({
      type: 'update',
      id: note.id,
      updates: { content, updatedAt: new Date().toISOString() }
    });
  };

  const handleTagsChange = (tags: string[]) => {
    dispatch({
      type: 'update',
      id: note.id,
      updates: { tags, updatedAt: new Date().toISOString() }
    });
  };

  return (
    <div className="min-h-screen bg-[#1a1d23] text-gray-100">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="mb-6">
          <BreadcrumbNav
            ancestors={[]}
            onCrumbClick={(noteId: string) => navigate(`/notes/${noteId}`)}
          />
        </div>

        {/* Note Title */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-100">
            {note.content ? note.content.split('\n')[0] || 'Untitled Note' : 'Untitled Note'}
          </h1>
          <div className="text-sm text-gray-400 mt-1">
            Created: {new Date(note.createdAt).toLocaleDateString()}
            {note.updatedAt !== note.createdAt && (
              <span> • Updated: {new Date(note.updatedAt).toLocaleDateString()}</span>
            )}
          </div>
        </div>

        {/* Tags */}
        <div className="mb-6">
          <TagManager
            tags={note.tags}
            onTagsChange={handleTagsChange}
            availableTags={[]} // You can populate this with existing tags from other notes
          />
        </div>

        {/* Note Content */}
        <div className="mb-6">
          <SimpleNoteEditor
            content={note.content}
            onChange={handleContentChange}
            placeholder="Start typing your note here... Use • or - for bullet points"
            className="w-full"
          />
        </div>

        {/* Footer Actions */}
        <div className="flex justify-between items-center pt-6 border-t border-gray-700">
          <button
            onClick={() => navigate('/notes')}
            className="flex items-center text-gray-400 hover:text-gray-300 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Notes
          </button>
          
          <div className="text-xs text-gray-500">
            Simple Note View
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleNoteDetailPage;
