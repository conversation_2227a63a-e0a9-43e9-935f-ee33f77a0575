import type { Note } from '../types';

export function flattenTree(notes: Record<string, Note>, rootIds: string[]): Note[] {
  // TODO: Implement flattening logic
  return [];
}

export function rebuildTree(flatNotes: Note[]): { notes: Record<string, Note>; rootIds: string[] } {
  // TODO: Implement rebuilding logic
  return { notes: {}, rootIds: [] };
}

export const getNoteAncestors = (noteId: string, notes: Record<string, Note>): Note[] => {
  const ancestors: Note[] = [];
  let currentNote: Note | undefined = notes[noteId];
  
  while (currentNote && currentNote.parentId) {
    const parent: Note | undefined = notes[currentNote.parentId];
    if (parent) {
      ancestors.unshift(parent);
      currentNote = parent;
    } else {
      break;
    }
  }
  
  return ancestors;
};

export const getNoteDescendants = (noteId: string, notes: Record<string, Note>): string[] => {
  const descendants: string[] = [];
  const note = notes[noteId];
  
  if (!note) return descendants;
  
  const traverse = (id: string) => {
    const n = notes[id];
    if (n) {
      descendants.push(id);
      n.children.forEach(traverse);
    }
  };
  
  traverse(noteId);
  return descendants;
};

export const getNoteSiblings = (noteId: string, notes: Record<string, Note>): string[] => {
  const note = notes[noteId];
  if (!note) return [];
  
  if (note.parentId) {
    const parent = notes[note.parentId];
    return parent ? parent.children : [];
  }
  
  return Object.values(notes).filter(n => n.parentId === null).map(n => n.id);
};

export const getNextSibling = (noteId: string, notes: Record<string, Note>): string | null => {
  const siblings = getNoteSiblings(noteId, notes);
  const currentIndex = siblings.indexOf(noteId);
  if (currentIndex < siblings.length - 1) {
    return siblings[currentIndex + 1];
  }
  return null;
};

export const getPreviousSibling = (noteId: string, notes: Record<string, Note>): string | null => {
  const siblings = getNoteSiblings(noteId, notes);
  const currentIndex = siblings.indexOf(noteId);
  if (currentIndex > 0) {
    return siblings[currentIndex - 1];
  }
  return null;
};

export const getNoteDepth = (noteId: string, notes: Record<string, Note>): number => {
  let depth = 0;
  let currentNote: Note | undefined = notes[noteId];
  
  while (currentNote && currentNote.parentId) {
    depth++;
    currentNote = notes[currentNote.parentId];
  }
  
  return depth;
};

export const searchNotes = (query: string, notes: Record<string, Note>, rootIds: string[]): string[] => {
  if (!query.trim()) {
    return rootIds;
  }
  
  const searchLower = query.toLowerCase();
  const matchingIds = new Set<string>();
  
  const searchRecursive = (noteIds: string[]) => {
    noteIds.forEach(id => {
      const note = notes[id];
      if (note && note.content.toLowerCase().includes(searchLower)) {
        matchingIds.add(id);
      }
      if (note && note.children.length > 0) {
        searchRecursive(note.children);
      }
    });
  };
  
  searchRecursive(rootIds);
  return Array.from(matchingIds);
}; 