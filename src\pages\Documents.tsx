import React, { useState, useMemo } from 'react';
import { FiSearch, FiGrid, FiList, FiUpload, FiFile, FiImage, FiFileText, FiBarChart2, FiCode, FiArchive, FiEye, FiDownload, FiX } from 'react-icons/fi';
import type { Document, Project, Comment } from '../App';
import DocumentUploader from '../components/DocumentUploader';
import DocumentPreview from '../components/DocumentPreview';
import AdvancedSearch from '../components/AdvancedSearch';
import { SearchResult } from '../utils/contentSearch';
import ocrService from '../utils/ocrService';
import usersData from '../App'; // If users are in App state, import or pass them as needed

interface DocumentsProps {
  documents: Document[];
  projects: Project[];
  onAddDocument: (document: Omit<Document, 'id' | 'uploadedAt' | 'lastModified' | 'version'>) => Document;
  onUpdateDocument: (documentId: string, updates: Partial<Document>) => void;
  onDeleteDocument: (documentId: string) => void;
  onShareDocument: (documentId: string, userIds: string[], visibility: 'private' | 'team' | 'public') => void;
  onAddDocumentComment: (documentId: string, comment: Omit<Comment, 'id' | 'timestamp' | 'isEdited'>) => void;
  users: { id: string; name: string; avatar?: string }[];
}

const Documents: React.FC<DocumentsProps> = ({
  documents,
  projects,
  onAddDocument,
  onUpdateDocument,
  onDeleteDocument,
  onShareDocument,
  onAddDocumentComment,
  users
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [showUploader, setShowUploader] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearchMode, setIsSearchMode] = useState(false);

  const filteredDocuments = useMemo(() => {
    if (isSearchMode && searchResults.length > 0) {
      // Show search results
      return documents.filter(doc => 
        searchResults.some(result => result.documentId === doc.id)
      );
    }

    // Apply regular filters
    return documents.filter(doc => {
      const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesType = selectedType === 'all' || doc.type === selectedType;
      return matchesSearch && matchesType;
    });
  }, [documents, searchQuery, selectedType, searchResults, isSearchMode]);

  const documentTypes = useMemo(() => {
    const types = Array.from(new Set(documents.map(doc => doc.type)));
    return ['all', ...types];
  }, [documents]);

  const handleUpload = (files: File[], tags: string[], projectId?: string, description?: string) => {
    files.forEach(async (file) => {
      const documentType = getDocumentType(file.type);
      const newDocument = {
        name: file.name,
        originalName: file.name,
        type: documentType,
        mimeType: file.type,
        size: file.size,
        tags,
        projectId,
        description,
        uploadedBy: 'user1', // TODO: Get from auth context
        isShared: false,
        visibility: 'private' as const,
        comments: [],
        version: 1,
        processingStatus: 'pending' as const
      };
      
      // Add document and get the created document with ID
      const addedDocument = onAddDocument(newDocument);
      
      // Process document for OCR and preview if we have a valid document
      if (addedDocument && addedDocument.id) {
        await processDocument(file, documentType, addedDocument.id);
      }
    });
    setShowUploader(false);
  };

  const processDocument = async (file: File, documentType: Document['type'], documentId?: string) => {
    if (!documentId) return;

    try {
      // Update processing status
      onUpdateDocument(documentId, { processingStatus: 'processing' });

      let ocrText: string | undefined;
      let previewData: any = {};

      // Extract text content based on document type
      if (documentType === 'image' || documentType === 'pdf') {
        // Perform OCR
        try {
          const ocrResult = await ocrService.extractTextFromImage(file);
          ocrText = ocrResult.text;
        } catch (error) {
          console.warn('OCR failed:', error);
        }
      }

      // Generate preview data
      if (documentType === 'image') {
        previewData = {
          type: 'image',
          imageUrl: URL.createObjectURL(file)
        };
      } else if (documentType === 'pdf') {
        previewData = {
          type: 'pdf'
        };
        // Store blob URL for PDF preview
        onUpdateDocument(documentId, { url: URL.createObjectURL(file) });
      } else if (documentType === 'code') {
        const textContent = await file.text();
        previewData = {
          type: 'code',
          content: textContent,
          codeLanguage: getCodeLanguage(file.name)
        };
      } else if (documentType === 'document' || documentType === 'presentation') {
        const textContent = await file.text();
        previewData = {
          type: 'text',
          content: textContent
        };
      } else if (documentType === 'spreadsheet') {
        // For spreadsheets, we'd typically parse CSV or Excel
        // This is a simplified version
        const textContent = await file.text();
        previewData = {
          type: 'spreadsheet',
          spreadsheetData: {
            sheets: [{
              name: 'Sheet1',
              data: textContent.split('\n').map(row => row.split(','))
            }]
          }
        };
      }

      // Update document with processed data
      onUpdateDocument(documentId, {
        ocrText,
        contentText: previewData.content,
        previewData,
        processingStatus: 'completed'
      });

    } catch (error) {
      console.error('Document processing failed:', error);
      onUpdateDocument(documentId, {
        processingStatus: 'failed',
        processingError: error instanceof Error ? error.message : 'Processing failed'
      });
    }
  };

  const getCodeLanguage = (filename: string): string => {
    const ext = filename.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'JavaScript',
      'ts': 'TypeScript',
      'jsx': 'React JSX',
      'tsx': 'React TSX',
      'py': 'Python',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C',
      'cs': 'C#',
      'php': 'PHP',
      'rb': 'Ruby',
      'go': 'Go',
      'rs': 'Rust',
      'swift': 'Swift',
      'kt': 'Kotlin',
      'scala': 'Scala',
      'html': 'HTML',
      'css': 'CSS',
      'scss': 'SCSS',
      'sass': 'Sass',
      'json': 'JSON',
      'xml': 'XML',
      'md': 'Markdown',
      'sql': 'SQL',
      'sh': 'Shell',
      'yml': 'YAML',
      'yaml': 'YAML'
    };
    return languageMap[ext || ''] || 'Text';
  };

  const getDocumentType = (mimeType: string): Document['type'] => {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType === 'application/pdf') return 'pdf';
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel') || mimeType.includes('csv')) return 'spreadsheet';
    if (mimeType.includes('document') || mimeType.includes('word')) return 'document';
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return 'presentation';
    if (mimeType.includes('text/') || mimeType.includes('javascript') || mimeType.includes('json')) return 'code';
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar')) return 'archive';
    return 'other';
  };

  const handleDocumentClick = (document: Document) => {
    setSelectedDocument(document);
    setShowPreview(true);
  };

  const handleSearchResults = (results: SearchResult[]) => {
    setSearchResults(results);
    setIsSearchMode(results.length > 0);
  };

  const handleSearchDocumentClick = (documentId: string) => {
    const document = documents.find(doc => doc.id === documentId);
    if (document) {
      handleDocumentClick(document);
    }
  };

  const handleDownload = (document: Document) => {
    // Create a blob URL for the document content
    let blob: Blob;
    let filename = document.name;
    
    if (document.previewData?.content) {
      // For text-based documents, create a blob from the content
      blob = new Blob([document.previewData.content], { 
        type: document.mimeType || 'text/plain' 
      });
    } else if (document.previewData?.imageUrl) {
      // For images, we'd need to fetch the image data
      // This is a simplified version - in a real app you'd fetch the actual file
      console.log('Downloading image:', document.name);
      return;
    } else {
      // For other files, create a placeholder blob
      blob = new Blob(['File content not available'], { type: 'text/plain' });
      filename = `${document.name}.txt`;
    }
    
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = window.document.createElement('a');
    link.href = url;
    link.download = filename;
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    console.log('Downloading document:', document.name);
  };

  const handleUpdateTags = (documentId: string, tags: string[]) => {
    onUpdateDocument(documentId, { tags });
  };

  const handleUpdateAccess = (documentId: string, access: { sharedWith: string[]; visibility: 'private' | 'team' | 'public' }) => {
    onUpdateDocument(documentId, access);
  };

  return (
    <div className="p-4 pl-8 flex flex-col w-full space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Documents</h1>
          <p className="text-gray-600 dark:text-gray-400">
            {filteredDocuments.length} of {documents.length} documents
          </p>
        </div>
        <button 
          onClick={() => setShowUploader(true)}
          className="flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
        >
          <FiUpload className="mr-2" />
          Upload Documents
        </button>
      </div>

      {/* Advanced Search */}
      <AdvancedSearch
        documents={documents}
        onSearchResults={handleSearchResults}
        onDocumentClick={handleSearchDocumentClick}
        className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm"
      />

      {/* Filters and View Toggle */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search documents..."
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setIsSearchMode(false);
                }}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>
          </div>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            {documentTypes.map(type => (
              <option key={type} value={type}>
                {type === 'all' ? 'All Types' : type.charAt(0).toUpperCase() + type.slice(1)}
              </option>
            ))}
          </select>

          <div className="flex border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
            <button
              onClick={() => setViewMode('grid')}
              className={`px-3 py-2 ${viewMode === 'grid' ? 'bg-red-500 text-white' : 'bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400'}`}
            >
              <FiGrid />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-2 ${viewMode === 'list' ? 'bg-red-500 text-white' : 'bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400'}`}
            >
              <FiList />
            </button>
          </div>
        </div>
      </div>

      {filteredDocuments.length === 0 ? (
        <div className="text-center py-12">
          <FiFile className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No documents found</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Get started by uploading your first document
          </p>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {filteredDocuments.map(doc => (
                <div
                  key={doc.id}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => handleDocumentClick(doc)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      {doc.type === 'pdf' && <FiFile className="text-red-500 mr-2" />}
                      {doc.type === 'image' && <FiImage className="text-green-500 mr-2" />}
                      {doc.type === 'document' && <FiFileText className="text-blue-500 mr-2" />}
                      {doc.type === 'spreadsheet' && <FiBarChart2 className="text-green-600 mr-2" />}
                      {doc.type === 'code' && <FiCode className="text-purple-500 mr-2" />}
                      {doc.type === 'archive' && <FiArchive className="text-gray-500 mr-2" />}
                      <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {doc.name}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={e => { e.stopPropagation(); handleDownload(doc); }}
                        className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        title="Download"
                      >
                        <FiDownload size={14} />
                      </button>
                    </div>
                  </div>
                  
                  {/* Processing Status */}
                  {doc.processingStatus && (
                    <div className="mb-2">
                      {doc.processingStatus === 'processing' && (
                        <div className="flex items-center text-xs text-blue-600 dark:text-blue-400">
                          <div className="animate-spin rounded-full h-3 w-3 border-b border-current mr-1"></div>
                          Processing...
                        </div>
                      )}
                      {doc.processingStatus === 'completed' && doc.ocrText && (
                        <div className="text-xs text-green-600 dark:text-green-400">
                          ✓ OCR Available
                        </div>
                      )}
                      {doc.processingStatus === 'failed' && (
                        <div className="text-xs text-red-600 dark:text-red-400">
                          ✗ Processing failed
                        </div>
                      )}
                    </div>
                  )}

                  <div className="flex flex-wrap gap-1 mb-2">
                    {doc.tags.slice(0, 3).map(tag => (
                      <span key={tag} className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200 px-2 py-1 rounded-full text-xs">
                        {tag}
                      </span>
                    ))}
                    {doc.tags.length > 3 && (
                      <span className="text-gray-500 text-xs">+{doc.tags.length - 3} more</span>
                    )}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {(doc.size / 1024 / 1024).toFixed(2)} MB
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredDocuments.map(doc => (
                <div key={doc.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                  <div className="flex items-center space-x-3">
                    {doc.type === 'pdf' && <FiFile className="text-red-500" />}
                    {doc.type === 'image' && <FiImage className="text-green-500" />}
                    {doc.type === 'document' && <FiFileText className="text-blue-500" />}
                    {doc.type === 'spreadsheet' && <FiBarChart2 className="text-green-600" />}
                    {doc.type === 'code' && <FiCode className="text-purple-500" />}
                    {doc.type === 'archive' && <FiArchive className="text-gray-500" />}
                    <span className="font-medium text-gray-900 dark:text-white">{doc.name}</span>
                    
                    {/* Processing Status */}
                    {doc.processingStatus === 'completed' && doc.ocrText && (
                      <span className="text-xs bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-2 py-1 rounded-full">
                        OCR
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                    <span>{(doc.size / 1024 / 1024).toFixed(2)} MB</span>
                    <span>{new Date(doc.uploadedAt).toLocaleDateString()}</span>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={e => { e.stopPropagation(); handleDownload(doc); }}
                        className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        title="Download"
                      >
                        <FiDownload size={14} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Document Uploader Modal */}
      {showUploader && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Upload Documents</h2>
              <button
                onClick={() => setShowUploader(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <FiX size={24} />
              </button>
            </div>
            <DocumentUploader
              projects={projects}
              onUpload={handleUpload}
              onCancel={() => setShowUploader(false)}
            />
          </div>
        </div>
      )}

      {/* Document Preview Modal */}
      {selectedDocument && (
        <DocumentPreview
          document={selectedDocument}
          isOpen={showPreview}
          onClose={() => {
            setShowPreview(false);
            setSelectedDocument(null);
          }}
          onDownload={handleDownload}
          onDelete={(doc) => {
            onDeleteDocument(doc.id);
            setShowPreview(false);
            setSelectedDocument(null);
          }}
          onUpdateTags={handleUpdateTags}
          onUpdateAccess={handleUpdateAccess}
          users={users}
        />
      )}
    </div>
  );
};

export default Documents; 