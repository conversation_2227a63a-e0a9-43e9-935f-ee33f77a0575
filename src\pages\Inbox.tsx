import React, { useState } from 'react';
import type { Task, Project } from '../App';
import TaskCard from '../components/TaskCard';
import TaskFilterBar from '../components/TaskFilterBar';

// Placeholder for tags (future)
const TaskTags = ({ tags }: { tags: string[] }) => (
  <div className="flex gap-2 mt-2 flex-wrap">
    {tags.map(tag => (
      <span key={tag} className="bg-blue-800 text-blue-200 text-xs px-2 py-0.5 rounded-full">{tag}</span>
    ))}
  </div>
);

type InboxProps = {
  tasks: Task[];
  projects?: Project[];
  onTaskClick?: (task: Task) => void;
  onToggleComplete?: (task: Task, completed: boolean) => void;
};

const Inbox: React.FC<InboxProps> = ({ tasks, projects = [], onTaskClick, onToggleComplete }) => {
  const [filterProject, setFilterProject] = useState<string | null>(null);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>(tasks);

  // Filter tasks based on selected project
  const projectFilteredTasks = filterProject 
    ? filteredTasks.filter(task => task.projectId === filterProject)
    : filteredTasks.filter(task => !task.projectId); // Show only inbox tasks when no filter

  const selectedProject = projects.find(p => p.id === filterProject);

  return (
    <div className="p-4 pl-8 flex flex-col w-full">
      <div className="flex items-center justify-between mb-4 w-full max-w-md">
        <h2 className="text-2xl font-semibold">
          {selectedProject ? selectedProject.name : 'Inbox'}
        </h2>
        {selectedProject && (
          <button
            onClick={() => setFilterProject(null)}
            className="text-gray-400 hover:text-gray-200 text-sm"
          >
            ← Back to Inbox
          </button>
        )}
      </div>
      
      {/* Task Filter Bar */}
      <div className="mb-6">
        <TaskFilterBar 
          tasks={tasks} 
          onFilterChange={setFilteredTasks}
          className="mb-4"
        />
      </div>
      
      {/* Project filter */}
      {projects.length > 0 && !selectedProject && (
        <div className="mb-4">
          <div className="text-sm text-gray-400 mb-2">Filter by project:</div>
          <div className="flex flex-wrap gap-2">
            {projects.map(project => (
              <button
                key={project.id}
                onClick={() => setFilterProject(project.id)}
                className="flex items-center gap-2 px-3 py-1 rounded-full text-sm bg-gray-800 text-gray-300 hover:bg-gray-700 transition-colors"
              >
                <div 
                  className="w-3 h-3 rounded"
                  style={{ backgroundColor: project.color }}
                />
                {project.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {projectFilteredTasks.length === 0 ? (
        <div className="text-gray-600 mt-4">
          {selectedProject 
            ? `No tasks in ${selectedProject.name} yet.` 
            : 'This is your inbox. Tasks will appear here.'
          }
        </div>
      ) : (
        <div className="flex flex-col w-full max-w-md">
          {projectFilteredTasks.map(task => (
            <TaskCard
              key={task.id}
              task={task}
              project={projects.find(p => p.id === task.projectId)}
              onClick={() => onTaskClick?.(task)}
              onToggleComplete={completed => onToggleComplete?.(task, completed)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Inbox; 