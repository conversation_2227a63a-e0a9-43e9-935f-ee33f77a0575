import React from 'react';

const Login: React.FC = () => (
  <div className="p-4 flex flex-col items-center justify-center min-h-screen">
    <h2 className="text-2xl font-semibold mb-4">Login</h2>
    <div className="text-gray-600 mb-4">Please log in to access your account.</div>
    <form className="w-full max-w-xs space-y-4">
      <input className="w-full px-3 py-2 border rounded" type="email" placeholder="Email" />
      <input className="w-full px-3 py-2 border rounded" type="password" placeholder="Password" />
      <button className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700" type="submit">Login</button>
    </form>
  </div>
);

export default Login; 