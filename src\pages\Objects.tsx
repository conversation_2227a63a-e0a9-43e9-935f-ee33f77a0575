import React, { useState } from 'react';
import FileUploader from '../components/FileUploader';
import ObjectGallery from '../components/ObjectGallery';
import TagFilterBar from '../components/TagFilterBar';
import GraphViewModal from '../components/GraphViewModal';

interface UploadedObject {
  id: string;
  name: string;
  type: string;
  tags: string[];
}

const Objects: React.FC = () => {
  const [objects, setObjects] = useState<UploadedObject[]>([]);
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [graphOpen, setGraphOpen] = useState(false);

  const handleUpload = (files: File[], tags: string[]) => {
    const newObjects = files.map((file) => ({
      id: `${file.name}-${Date.now()}`,
      name: file.name,
      type: file.type,
      tags,
    }));
    setObjects((prev) => [...newObjects, ...prev]);
  };

  // Extract unique tags from all objects
  const allTags = Array.from(new Set(objects.flatMap((obj) => obj.tags)));

  // Filter objects by selected tag
  const filteredObjects = selectedTag
    ? objects.filter((obj) => obj.tags.includes(selectedTag))
    : objects;

  return (
    <div className="p-4 max-w-4xl mx-auto">
      <h2 className="text-2xl font-semibold mb-4">Objects Library</h2>
      <FileUploader onUpload={handleUpload} />
      <TagFilterBar tags={allTags} selectedTag={selectedTag} onTagSelect={setSelectedTag} />
      {selectedTag && (
        <button className="mb-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" onClick={() => setGraphOpen(true)}>
          Open Graph View for "{selectedTag}"
        </button>
      )}
      <ObjectGallery objects={filteredObjects} />
      {selectedTag && (
        <GraphViewModal
          open={graphOpen}
          onClose={() => setGraphOpen(false)}
          tag={selectedTag}
          objects={filteredObjects}
        />
      )}
    </div>
  );
};

export default Objects; 