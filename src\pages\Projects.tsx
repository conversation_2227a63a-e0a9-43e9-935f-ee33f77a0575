import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>rid, <PERSON><PERSON>ist, FiSearch } from 'react-icons/fi';
import ProjectCard from '../components/ProjectCard';
import ProjectForm from '../components/ProjectForm';
import TaskCard from '../components/TaskCard';
import TaskFilterBar from '../components/TaskFilterBar';
import KanbanBoard from '../components/KanbanBoard';
import ViewToggle, { ViewMode } from '../components/ViewToggle';
import type { Project, Task } from '../App';
import TaskForm from '../components/TaskForm';
import { useParams } from 'react-router-dom';

interface ProjectsProps {
  projects: Project[];
  tasks: Task[];
  onAddProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt' | 'taskCount' | 'completedTaskCount'>) => void;
  onUpdateProject: (project: Project) => void;
  onDeleteProject: (projectId: string) => void;
  onToggleProjectFavorite: (projectId: string) => void;
  onTaskClick: (task: Task) => void;
  onToggleComplete: (task: Task, completed: boolean) => void;
  onUpdateTaskStatus?: (taskId: string, newStatus: string) => void;
}

const Projects: React.FC<ProjectsProps> = ({
  projects,
  tasks,
  onAddProject,
  onUpdateProject,
  onDeleteProject,
  onToggleProjectFavorite,
  onTaskClick,
  onToggleComplete,
  onUpdateTaskStatus
}) => {
  const { id } = useParams<{ id?: string }>();
  const [showProjectForm, setShowProjectForm] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [taskViewMode, setTaskViewMode] = useState<ViewMode>('list');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterFavorites, setFilterFavorites] = useState(false);
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [filteredProjectTasks, setFilteredProjectTasks] = useState<Task[]>([]);

  // Set selectedProject based on route param
  useEffect(() => {
    if (id) {
      const found = projects.find(p => p.id === id);
      setSelectedProject(found || null);
    } else {
      setSelectedProject(null);
    }
  }, [id, projects]);

  // Get tasks for selected project with proper memoization
  const projectTasks = useMemo(() => {
    return selectedProject 
      ? tasks.filter(task => task.projectId === selectedProject.id)
      : [];
  }, [selectedProject, tasks]);

  const handleEditProject = (project: Project) => {
    setEditingProject(project);
    setShowProjectForm(true);
  };

  const handleSaveProject = (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt' | 'taskCount' | 'completedTaskCount'>) => {
    if (editingProject) {
      onUpdateProject({ ...editingProject, ...projectData });
    } else {
      onAddProject(projectData);
    }
    setShowProjectForm(false);
    setEditingProject(null);
  };

  const handleCancelProjectForm = () => {
    setShowProjectForm(false);
    setEditingProject(null);
  };

  // Filter projects based on search and favorites
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFavorites = !filterFavorites || project.isFavorite;
    return matchesSearch && matchesFavorites;
  });

  // Sort projects: favorites first, then by name
  const sortedProjects = [...filteredProjects].sort((a, b) => {
    if (a.isFavorite && !b.isFavorite) return -1;
    if (!a.isFavorite && b.isFavorite) return 1;
    return a.name.localeCompare(b.name);
  });

  return (
    <div className="p-4 pl-8 flex flex-col w-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-semibold text-gray-100 mb-1">Projects</h2>
          <p className="text-gray-400">
            {selectedProject 
              ? `${selectedProject.name} • ${projectTasks.length} tasks`
              : `${projects.length} projects • ${tasks.length} total tasks`
            }
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          {/* Search */}
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="bg-gray-800 border border-gray-700 rounded-lg pl-10 pr-4 py-2 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-blue-500 w-64"
            />
          </div>

          {/* View mode toggle */}
          <div className="flex bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded transition-colors ${
                viewMode === 'grid' 
                  ? 'bg-blue-600 text-white' 
                  : 'text-gray-400 hover:text-gray-200'
              }`}
            >
              <FiGrid size={16} />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded transition-colors ${
                viewMode === 'list' 
                  ? 'bg-blue-600 text-white' 
                  : 'text-gray-400 hover:text-gray-200'
              }`}
            >
              <FiList size={16} />
            </button>
          </div>

          {/* Add project button */}
          <button
            onClick={() => setShowProjectForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <FiPlus size={16} />
            New Project
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => setFilterFavorites(!filterFavorites)}
          className={`px-3 py-1 rounded-full text-sm transition-colors ${
            filterFavorites 
              ? 'bg-yellow-600 text-white' 
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
          }`}
        >
          Favorites only
        </button>
        
        {selectedProject && (
          <button
            onClick={() => setSelectedProject(null)}
            className="text-gray-400 hover:text-gray-200 text-sm"
          >
            ← Back to all projects
          </button>
        )}
      </div>

      {/* Content */}
      {selectedProject ? (
        // Project detail view
        <div>
          <div className="mb-6">
            <div className="flex items-center gap-3 mb-4">
              <div 
                className="w-8 h-8 rounded-lg flex items-center justify-center"
                style={{ backgroundColor: selectedProject.color }}
              >
                <span className="text-white font-semibold">
                  {selectedProject.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <h3 className="text-xl font-semibold text-gray-100">
                {selectedProject.name}
              </h3>
            </div>
            <p className="text-gray-400 mb-4">{selectedProject.description}</p>
            <button
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition mb-4"
              onClick={() => setShowTaskForm(true)}
            >
              <FiPlus className="inline mr-2" /> Add Task
            </button>
          </div>

          {/* Task Filter Bar for project tasks */}
          {projectTasks.length > 0 && taskViewMode === 'list' && (
            <div className="mb-6">
              <TaskFilterBar 
                tasks={projectTasks} 
                onFilterChange={setFilteredProjectTasks}
                className="mb-4"
              />
            </div>
          )}

          {/* Task View Toggle */}
          {projectTasks.length > 0 && (
            <div className="mb-6 flex items-center justify-between">
              <ViewToggle
                currentView={taskViewMode}
                onViewChange={setTaskViewMode}
              />
              <div className="text-sm text-gray-400">
                {projectTasks.length} task{projectTasks.length !== 1 ? 's' : ''}
              </div>
            </div>
          )}

          {projectTasks.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">No tasks in this project yet</div>
              <button className="text-blue-400 hover:text-blue-300" onClick={() => setShowTaskForm(true)}>
                Add your first task
              </button>
            </div>
          ) : (
            <>
              {taskViewMode === 'list' ? (
                <div className="space-y-2">
                  {(filteredProjectTasks.length > 0 ? filteredProjectTasks : projectTasks).map(task => (
                    <TaskCard
                      key={task.id}
                      task={task}
                      project={selectedProject}
                      onClick={() => onTaskClick(task)}
                      onToggleComplete={completed => onToggleComplete(task, completed)}
                    />
                  ))}
                </div>
              ) : (
                <KanbanBoard
                  tasks={projectTasks}
                  project={selectedProject}
                  onTaskClick={onTaskClick}
                  onToggleComplete={onToggleComplete}
                  onUpdateTaskStatus={onUpdateTaskStatus}
                  onAddTask={(status) => {
                    setShowTaskForm(true);
                    // You could pre-populate the form with the status
                  }}
                />
              )}
            </>
          )}
          {/* TaskForm Modal for project */}
          {showTaskForm && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
              <div className="relative">
                <button
                  className="absolute top-2 right-2 text-gray-400 hover:text-gray-200 text-2xl z-10"
                  onClick={() => setShowTaskForm(false)}
                  aria-label="Close"
                >
                  &times;
                </button>
                <TaskForm
                  onAddTask={(task) => {
                    if (!task.projectId) task.projectId = selectedProject.id;
                    onTaskClick({ ...task, id: Date.now().toString(), comments: [], completed: false });
                    setShowTaskForm(false);
                  }}
                  projects={projects}
                  defaultProjectId={selectedProject.id}
                  onCancel={() => setShowTaskForm(false)}
                />
              </div>
            </div>
          )}
        </div>
      ) : (
        // Projects grid/list view
        <div>
          {sortedProjects.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                {searchTerm || filterFavorites 
                  ? 'No projects match your filters' 
                  : 'No projects yet'
                }
              </div>
              {!searchTerm && !filterFavorites && (
                <button
                  onClick={() => setShowProjectForm(true)}
                  className="text-blue-400 hover:text-blue-300"
                >
                  Create your first project
                </button>
              )}
            </div>
          ) : (
            <div className={
              viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                : 'space-y-4'
            }>
              {sortedProjects.map(project => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  onToggleFavorite={onToggleProjectFavorite}
                  onEdit={handleEditProject}
                  onDelete={onDeleteProject}
                  onClick={() => setSelectedProject(project)}
                />
              ))}
            </div>
          )}
        </div>
      )}

      {/* Project Form Modal */}
      {showProjectForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
          <ProjectForm
            project={editingProject || undefined}
            onSave={handleSaveProject}
            onCancel={handleCancelProjectForm}
          />
        </div>
      )}
    </div>
  );
};

export default Projects; 