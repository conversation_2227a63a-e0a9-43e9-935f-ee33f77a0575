import React from 'react';
import { <PERSON>Bell, FiVolume2, FiVolumeX, FiMonitor, FiShield } from 'react-icons/fi';

type NotificationSettings = {
  enabled: boolean;
  permission: NotificationPermission;
  dueDateReminders: boolean;
  customReminders: boolean;
  soundEnabled: boolean;
  desktopNotifications: boolean;
};

type SettingsProps = {
  notificationSettings?: NotificationSettings;
  onUpdateNotificationSettings?: (settings: Partial<NotificationSettings>) => void;
  onRequestPermission?: () => Promise<void>;
};

const Settings: React.FC<SettingsProps> = ({
  notificationSettings = {
    enabled: false,
    permission: 'default',
    dueDateReminders: true,
    customReminders: true,
    soundEnabled: true,
    desktopNotifications: true
  },
  onUpdateNotificationSettings,
  onRequestPermission
}) => {
  const handleToggle = (key: keyof NotificationSettings) => {
    if (onUpdateNotificationSettings) {
      onUpdateNotificationSettings({ [key]: !notificationSettings[key] });
    }
  };

  const getPermissionStatus = () => {
    switch (notificationSettings.permission) {
      case 'granted':
        return { text: 'Granted', color: 'text-green-400', icon: '✓' };
      case 'denied':
        return { text: 'Denied', color: 'text-red-400', icon: '✗' };
      default:
        return { text: 'Not set', color: 'text-yellow-400', icon: '?' };
    }
  };

  const permissionStatus = getPermissionStatus();

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h2 className="text-3xl font-bold text-gray-100 mb-8">Settings</h2>
      
      {/* Notification Settings */}
      <div className="bg-gray-900 rounded-xl p-6 border border-gray-700 mb-6">
        <div className="flex items-center mb-6">
          <FiBell className="text-2xl text-blue-400 mr-3" />
          <h3 className="text-xl font-semibold text-gray-100">Notifications & Reminders</h3>
        </div>

        {/* Permission Status */}
        <div className="mb-6 p-4 bg-gray-800 rounded-lg border border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FiShield className="text-lg text-gray-400 mr-3" />
              <div>
                <h4 className="text-sm font-medium text-gray-300">Browser Permission</h4>
                <p className="text-sm text-gray-400">Allow notifications from this app</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <span className={`text-sm font-medium ${permissionStatus.color}`}>
                {permissionStatus.icon} {permissionStatus.text}
              </span>
              {notificationSettings.permission === 'default' && onRequestPermission && (
                <button
                  onClick={onRequestPermission}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                >
                  Request Permission
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Notification Toggles */}
        <div className="space-y-4">
          {/* Enable Notifications */}
          <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
            <div className="flex items-center">
              <FiBell className="text-lg text-gray-400 mr-3" />
              <div>
                <h4 className="text-sm font-medium text-gray-300">Enable Notifications</h4>
                <p className="text-sm text-gray-400">Receive notifications for task reminders</p>
              </div>
            </div>
            <button
              onClick={() => handleToggle('enabled')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notificationSettings.enabled ? 'bg-blue-600' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notificationSettings.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Desktop Notifications */}
          <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
            <div className="flex items-center">
              <FiMonitor className="text-lg text-gray-400 mr-3" />
              <div>
                <h4 className="text-sm font-medium text-gray-300">Desktop Notifications</h4>
                <p className="text-sm text-gray-400">Show notifications on your desktop</p>
              </div>
            </div>
            <button
              onClick={() => handleToggle('desktopNotifications')}
              disabled={!notificationSettings.enabled}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notificationSettings.desktopNotifications && notificationSettings.enabled ? 'bg-blue-600' : 'bg-gray-600'
              } ${!notificationSettings.enabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notificationSettings.desktopNotifications && notificationSettings.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Sound Notifications */}
          <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
            <div className="flex items-center">
              {notificationSettings.soundEnabled ? (
                <FiVolume2 className="text-lg text-gray-400 mr-3" />
              ) : (
                <FiVolumeX className="text-lg text-gray-400 mr-3" />
              )}
              <div>
                <h4 className="text-sm font-medium text-gray-300">Sound Notifications</h4>
                <p className="text-sm text-gray-400">Play sound when notifications appear</p>
              </div>
            </div>
            <button
              onClick={() => handleToggle('soundEnabled')}
              disabled={!notificationSettings.enabled}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notificationSettings.soundEnabled && notificationSettings.enabled ? 'bg-blue-600' : 'bg-gray-600'
              } ${!notificationSettings.enabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notificationSettings.soundEnabled && notificationSettings.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Due Date Reminders */}
          <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
            <div className="flex items-center">
              <FiBell className="text-lg text-gray-400 mr-3" />
              <div>
                <h4 className="text-sm font-medium text-gray-300">Due Date Reminders</h4>
                <p className="text-sm text-gray-400">Automatically remind about tasks due soon</p>
              </div>
            </div>
            <button
              onClick={() => handleToggle('dueDateReminders')}
              disabled={!notificationSettings.enabled}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notificationSettings.dueDateReminders && notificationSettings.enabled ? 'bg-blue-600' : 'bg-gray-600'
              } ${!notificationSettings.enabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notificationSettings.dueDateReminders && notificationSettings.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Custom Reminders */}
          <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
            <div className="flex items-center">
              <FiBell className="text-lg text-gray-400 mr-3" />
              <div>
                <h4 className="text-sm font-medium text-gray-300">Custom Reminders</h4>
                <p className="text-sm text-gray-400">Allow setting custom reminder times</p>
              </div>
            </div>
            <button
              onClick={() => handleToggle('customReminders')}
              disabled={!notificationSettings.enabled}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notificationSettings.customReminders && notificationSettings.enabled ? 'bg-blue-600' : 'bg-gray-600'
              } ${!notificationSettings.enabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notificationSettings.customReminders && notificationSettings.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Additional Settings Placeholder */}
      <div className="bg-gray-900 rounded-xl p-6 border border-gray-700">
        <h3 className="text-xl font-semibold text-gray-100 mb-4">Additional Settings</h3>
        <div className="text-gray-400">
          More settings and preferences will be added here in future updates.
        </div>
      </div>
    </div>
  );
};

export default Settings; 