import React, { useState, useMemo } from 'react';
import type { Task, Project } from '../App';
import TaskCard from '../components/TaskCard';
import TaskFilterBar from '../components/TaskFilterBar';

type TodayProps = {
  tasks: Task[];
  projects?: Project[];
  onTaskClick?: (task: Task) => void;
  onToggleComplete?: (task: Task, completed: boolean) => void;
};

const Today: React.FC<TodayProps> = ({ tasks, projects = [], onTaskClick, onToggleComplete }) => {
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);

  // Get tasks due today
  const todayTasks = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return tasks.filter(task => {
      if (!task.dueDate) return false;
      const taskDate = new Date(task.dueDate);
      taskDate.setHours(0, 0, 0, 0);
      return taskDate.getTime() === today.getTime();
    });
  }, [tasks]);

  return (
    <div className="p-4 pl-8 flex flex-col w-full">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-100 mb-2">Today</h2>
        <p className="text-gray-400">Tasks due today</p>
      </div>

      {/* Task Filter Bar */}
      {todayTasks.length > 0 && (
        <div className="mb-6">
          <TaskFilterBar 
            tasks={todayTasks} 
            onFilterChange={setFilteredTasks}
            className="mb-4"
          />
        </div>
      )}

      {todayTasks.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">No tasks due today</div>
          <div className="text-gray-500 text-sm">Tasks with today's due date will appear here</div>
        </div>
      ) : (
        <div className="flex flex-col w-full max-w-md">
          {(filteredTasks.length > 0 ? filteredTasks : todayTasks).map(task => (
            <TaskCard
              key={task.id}
              task={task}
              project={projects.find(p => p.id === task.projectId)}
              onClick={() => onTaskClick?.(task)}
              onToggleComplete={completed => onToggleComplete?.(task, completed)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Today; 