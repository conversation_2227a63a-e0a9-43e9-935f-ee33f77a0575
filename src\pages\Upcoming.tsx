import React, { useState, useMemo } from 'react';
import type { Task, Project } from '../App';
import TaskCard from '../components/TaskCard';
import TaskFilterBar from '../components/TaskFilterBar';

type UpcomingProps = {
  tasks: Task[];
  projects?: Project[];
  onTaskClick?: (task: Task) => void;
  onToggleComplete?: (task: Task, completed: boolean) => void;
};

const Upcoming: React.FC<UpcomingProps> = ({ tasks, projects = [], onTaskClick, onToggleComplete }) => {
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);

  // Get upcoming tasks (due in the next 7 days)
  const upcomingTasks = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);
    
    return tasks.filter(task => {
      if (!task.dueDate) return false;
      const taskDate = new Date(task.dueDate);
      taskDate.setHours(0, 0, 0, 0);
      return taskDate > today && taskDate <= nextWeek;
    });
  }, [tasks]);

  return (
    <div className="p-4 pl-8 flex flex-col w-full">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-100 mb-2">Upcoming</h2>
        <p className="text-gray-400">Tasks due in the next 7 days</p>
      </div>

      {/* Task Filter Bar */}
      {upcomingTasks.length > 0 && (
        <div className="mb-6">
          <TaskFilterBar 
            tasks={upcomingTasks} 
            onFilterChange={setFilteredTasks}
            className="mb-4"
          />
        </div>
      )}

      {upcomingTasks.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">No upcoming tasks</div>
          <div className="text-gray-500 text-sm">Tasks due in the next 7 days will appear here</div>
        </div>
      ) : (
        <div className="flex flex-col w-full max-w-md">
          {(filteredTasks.length > 0 ? filteredTasks : upcomingTasks).map(task => (
            <TaskCard
              key={task.id}
              task={task}
              project={projects.find(p => p.id === task.projectId)}
              onClick={() => onTaskClick?.(task)}
              onToggleComplete={completed => onToggleComplete?.(task, completed)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Upcoming; 