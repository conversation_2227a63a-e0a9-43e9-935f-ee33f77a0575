import React, { useState } from 'react';
import { FiUser, FiMail, FiShield, FiCircle, FiEdit2, FiPlus } from "react-icons/fi";

const initialUsers = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>', avatar: '', role: 'admin', isOnline: true },
  { id: '2', name: '<PERSON>', email: '<EMAIL>', avatar: '', role: 'member', isOnline: false },
  { id: '3', name: '<PERSON>', email: '<EMAIL>', avatar: '', role: 'viewer', isOnline: true },
  { id: '4', name: '<PERSON>', email: '<EMAIL>', avatar: '', role: 'owner', isOnline: true },
];

const roleColors = {
  owner: 'bg-purple-600',
  admin: 'bg-blue-600',
  member: 'bg-green-600',
  viewer: 'bg-gray-600',
};

const roleOptions = ['owner', 'admin', 'member', 'viewer'];

const Users: React.FC = () => {
  const [users, setUsers] = useState(initialUsers);
  const [search, setSearch] = useState('');
  const [inviteOpen, setInviteOpen] = useState(false);
  const [editUser, setEditUser] = useState(null as null | typeof initialUsers[0]);
  const [form, setForm] = useState({ name: '', email: '', role: 'member' });

  const filteredUsers = users.filter(u =>
    u.name.toLowerCase().includes(search.toLowerCase()) ||
    u.email.toLowerCase().includes(search.toLowerCase())
  );

  const openInvite = () => {
    setForm({ name: '', email: '', role: 'member' });
    setInviteOpen(true);
  };
  const closeInvite = () => setInviteOpen(false);

  const openEdit = (user: typeof initialUsers[0]) => {
    setEditUser(user);
    setForm({ name: user.name, email: user.email, role: user.role });
  };
  const closeEdit = () => setEditUser(null);

  const handleInvite = (e: React.FormEvent) => {
    e.preventDefault();
    setUsers([
      ...users,
      { id: (Date.now()).toString(), name: form.name, email: form.email, avatar: '', role: form.role, isOnline: false },
    ]);
    closeInvite();
  };

  const handleEdit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!editUser) return;
    setUsers(users.map(u => u.id === editUser.id ? { ...u, ...form } : u));
    closeEdit();
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-3xl font-bold text-gray-100 flex items-center gap-2">
          <FiUser className="text-blue-400" /> Users
        </h2>
        <button onClick={openInvite} className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded shadow">
          <FiPlus /> Invite User
        </button>
      </div>
      <input
        className="w-full mb-4 px-4 py-2 rounded bg-gray-800 border border-gray-700 text-gray-200 placeholder-gray-400"
        placeholder="Search users by name or email..."
        value={search}
        onChange={e => setSearch(e.target.value)}
      />
      <div className="bg-gray-900 rounded-xl p-6 border border-gray-700">
        <table className="min-w-full divide-y divide-gray-700">
          <thead>
            <tr>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase">Name</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase">Email</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase">Role</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase">Status</th>
              <th></th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-800">
            {filteredUsers.map((user) => (
              <tr key={user.id} className="hover:bg-gray-800 transition-colors">
                <td className="px-4 py-3 flex items-center gap-3">
                  <span className="inline-flex items-center justify-center w-9 h-9 rounded-full bg-gray-700 text-gray-300">
                    {user.avatar ? (
                      <img src={user.avatar} alt={user.name} className="w-9 h-9 rounded-full object-cover" />
                    ) : (
                      <FiUser className="w-5 h-5" />
                    )}
                  </span>
                  <span className="font-medium text-gray-100">{user.name}</span>
                </td>
                <td className="px-4 py-3 text-gray-300 flex items-center gap-2">
                  <FiMail className="w-4 h-4 text-gray-400" />
                  {user.email}
                </td>
                <td className="px-4 py-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold text-white ${roleColors[user.role as keyof typeof roleColors]}`}>{user.role}</span>
                </td>
                <td className="px-4 py-3">
                  <span className="flex items-center gap-1">
                    <FiCircle className={`w-3 h-3 ${user.isOnline ? 'text-green-400' : 'text-gray-500'}`} />
                    <span className={`text-xs ${user.isOnline ? 'text-green-400' : 'text-gray-400'}`}>{user.isOnline ? 'Online' : 'Offline'}</span>
                  </span>
                </td>
                <td className="px-4 py-3 text-right">
                  <button onClick={() => openEdit(user)} className="text-blue-400 hover:text-blue-200 p-1 rounded">
                    <FiEdit2 />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Invite Modal */}
      {inviteOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-gray-900 rounded-lg shadow-lg p-6 w-full max-w-md relative">
            <button onClick={closeInvite} className="absolute top-2 right-2 text-gray-400 hover:text-gray-200">&times;</button>
            <h3 className="text-xl font-semibold text-gray-100 mb-4">Invite User</h3>
            <form onSubmit={handleInvite} className="space-y-4">
              <input required className="w-full px-3 py-2 rounded bg-gray-800 border border-gray-700 text-gray-200" placeholder="Name" value={form.name} onChange={e => setForm(f => ({ ...f, name: e.target.value }))} />
              <input required type="email" className="w-full px-3 py-2 rounded bg-gray-800 border border-gray-700 text-gray-200" placeholder="Email" value={form.email} onChange={e => setForm(f => ({ ...f, email: e.target.value }))} />
              <select className="w-full px-3 py-2 rounded bg-gray-800 border border-gray-700 text-gray-200" value={form.role} onChange={e => setForm(f => ({ ...f, role: e.target.value }))}>
                {roleOptions.map(role => <option key={role} value={role}>{role}</option>)}
              </select>
              <button type="submit" className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">Invite</button>
            </form>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {editUser && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-gray-900 rounded-lg shadow-lg p-6 w-full max-w-md relative">
            <button onClick={closeEdit} className="absolute top-2 right-2 text-gray-400 hover:text-gray-200">&times;</button>
            <h3 className="text-xl font-semibold text-gray-100 mb-4">Edit User</h3>
            <form onSubmit={handleEdit} className="space-y-4">
              <input required className="w-full px-3 py-2 rounded bg-gray-800 border border-gray-700 text-gray-200" placeholder="Name" value={form.name} onChange={e => setForm(f => ({ ...f, name: e.target.value }))} />
              <input required type="email" className="w-full px-3 py-2 rounded bg-gray-800 border border-gray-700 text-gray-200" placeholder="Email" value={form.email} onChange={e => setForm(f => ({ ...f, email: e.target.value }))} />
              <select className="w-full px-3 py-2 rounded bg-gray-800 border border-gray-700 text-gray-200" value={form.role} onChange={e => setForm(f => ({ ...f, role: e.target.value }))}>
                {roleOptions.map(role => <option key={role} value={role}>{role}</option>)}
              </select>
              <button type="submit" className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">Save</button>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Users; 