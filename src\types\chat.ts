// Chat System Data Models

export type ChatChannel = {
  id: string;
  name: string;
  description?: string;
  type: 'public' | 'private' | 'direct' | 'project' | 'task';
  icon?: string;
  color?: string;
  // Channel metadata
  createdAt: string;
  createdBy: string;
  lastActivity: string;
  memberCount: number;
  isArchived: boolean;
  isMuted: boolean;
  // Channel settings
  settings: {
    allowFileUploads: boolean;
    allowThreads: boolean;
    allowReactions: boolean;
    requireApproval: boolean;
    autoArchive: boolean;
  };
  // Integration with existing data
  projectId?: string; // For project-specific channels
  taskId?: string; // For task-specific channels
  // Member management
  members: string[]; // User IDs
  admins: string[]; // User IDs with admin privileges
  // Direct message specific
  isDirectMessage?: boolean;
  participants?: string[]; // For DM channels
};

export type ChatMessage = {
  id: string;
  channelId: string;
  text: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  timestamp: string;
  editedAt?: string;
  isEdited: boolean;
  isDeleted: boolean;
  deletedAt?: string;
  deletedBy?: string;
  // Message content
  attachments?: ChatAttachment[];
  reactions?: ChatReaction[];
  mentions?: string[]; // User IDs mentioned
  // Threading
  threadId?: string; // Parent message ID for replies
  replyCount?: number;
  lastReplyAt?: string;
  // Message metadata
  clientMessageId?: string; // For optimistic updates
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  // Integration with tasks/projects
  taskId?: string; // If message references a task
  projectId?: string; // If message references a project
  documentId?: string; // If message references a document
  // Rich content
  blocks?: ChatBlock[];
  // System messages
  isSystemMessage?: boolean;
  systemType?: 'member_joined' | 'member_left' | 'channel_created' | 'task_created' | 'project_updated';
};

export type ChatAttachment = {
  id: string;
  type: 'image' | 'file' | 'video' | 'audio' | 'link';
  name: string;
  url: string;
  thumbnailUrl?: string;
  size?: number;
  mimeType?: string;
  duration?: number; // For audio/video
  width?: number; // For images/videos
  height?: number; // For images/videos
  // File metadata
  uploadedBy: string;
  uploadedAt: string;
  // Preview data
  preview?: {
    type: 'text' | 'image' | 'pdf' | 'code';
    content?: string;
    imageUrl?: string;
    codeLanguage?: string;
  };
};

export type ChatReaction = {
  emoji: string;
  count: number;
  users: string[]; // User IDs who reacted
};

export type ChatBlock = {
  type: 'text' | 'image' | 'file' | 'task' | 'project' | 'user' | 'code' | 'quote';
  content: any;
  // For task/project blocks
  taskId?: string;
  projectId?: string;
  userId?: string;
};

export type ChatThread = {
  id: string;
  parentMessageId: string;
  channelId: string;
  messages: ChatMessage[];
  participantCount: number;
  lastActivity: string;
  isResolved: boolean;
  resolvedBy?: string;
  resolvedAt?: string;
};

export type ChatPresence = {
  userId: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: string;
  customStatus?: string;
  emoji?: string;
  expiresAt?: string;
};

export type ChatNotification = {
  id: string;
  userId: string;
  channelId: string;
  messageId: string;
  type: 'mention' | 'reply' | 'reaction' | 'thread' | 'direct_message';
  isRead: boolean;
  createdAt: string;
  // Notification content
  title: string;
  body: string;
  actionUrl?: string;
};

export type ChatSearchResult = {
  messageId: string;
  channelId: string;
  channelName: string;
  text: string;
  authorName: string;
  timestamp: string;
  highlights: {
    field: string;
    snippet: string;
  }[];
};

export type ChatSettings = {
  userId: string;
  // Notification preferences
  notifications: {
    mentions: boolean;
    directMessages: boolean;
    channelMessages: boolean;
    threadReplies: boolean;
    reactions: boolean;
    sound: boolean;
    desktop: boolean;
    mobile: boolean;
    email: boolean;
  };
  // Display preferences
  display: {
    theme: 'light' | 'dark' | 'auto';
    fontSize: 'small' | 'medium' | 'large';
    compactMode: boolean;
    showAvatars: boolean;
    showTimestamps: boolean;
    showReadReceipts: boolean;
  };
  // Privacy settings
  privacy: {
    showOnlineStatus: boolean;
    showLastSeen: boolean;
    allowMentions: boolean;
    allowDirectMessages: boolean;
  };
  // Keyboard shortcuts
  shortcuts: {
    [key: string]: string;
  };
};

// Chat state management
export type ChatState = {
  channels: ChatChannel[];
  activeChannelId?: string;
  messages: { [channelId: string]: ChatMessage[] };
  threads: { [threadId: string]: ChatThread };
  presence: { [userId: string]: ChatPresence };
  notifications: ChatNotification[];
  unreadCounts: { [channelId: string]: number };
  searchResults: ChatSearchResult[];
  isLoading: boolean;
  error?: string;
};

// Chat actions
export type ChatAction = 
  | { type: 'SET_ACTIVE_CHANNEL'; payload: string }
  | { type: 'ADD_MESSAGE'; payload: { channelId: string; message: ChatMessage } }
  | { type: 'UPDATE_MESSAGE'; payload: { channelId: string; messageId: string; updates: Partial<ChatMessage> } }
  | { type: 'DELETE_MESSAGE'; payload: { channelId: string; messageId: string } }
  | { type: 'ADD_REACTION'; payload: { channelId: string; messageId: string; reaction: ChatReaction } }
  | { type: 'SET_PRESENCE'; payload: { userId: string; presence: ChatPresence } }
  | { type: 'MARK_AS_READ'; payload: { channelId: string; userId: string } }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string }; 