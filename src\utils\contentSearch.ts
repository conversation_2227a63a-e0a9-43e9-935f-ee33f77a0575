// Content Search Utility for full-text search within documents

export interface SearchResult {
  documentId: string;
  documentName: string;
  documentType: string;
  matches: SearchMatch[];
  relevanceScore: number;
  preview: string;
}

export interface SearchMatch {
  field: 'name' | 'content' | 'ocrText' | 'tags' | 'description';
  text: string;
  startIndex: number;
  endIndex: number;
  highlightedText: string;
}

export interface SearchOptions {
  caseSensitive?: boolean;
  includeOCR?: boolean;
  includeTags?: boolean;
  includeDescription?: boolean;
  maxResults?: number;
  minRelevanceScore?: number;
  searchFields?: Array<'name' | 'content' | 'ocrText' | 'tags' | 'description'>;
}

export interface SearchIndex {
  documentId: string;
  name: string;
  content: string;
  ocrText?: string;
  tags: string[];
  description?: string;
  type: string;
}

class ContentSearchService {
  private searchIndex: Map<string, SearchIndex> = new Map();

  /**
   * Index a document for search
   */
  indexDocument(document: any): void {
    const searchIndex: SearchIndex = {
      documentId: document.id,
      name: document.name || '',
      content: document.contentText || document.previewData?.content || '',
      ocrText: document.ocrText || '',
      tags: document.tags || [],
      description: document.description || '',
      type: document.type || 'other'
    };

    this.searchIndex.set(document.id, searchIndex);
  }

  /**
   * Remove a document from search index
   */
  removeFromIndex(documentId: string): void {
    this.searchIndex.delete(documentId);
  }

  /**
   * Update document in search index
   */
  updateIndex(document: any): void {
    this.indexDocument(document);
  }

  /**
   * Search documents by query
   */
  search(
    query: string,
    options: SearchOptions = {}
  ): SearchResult[] {
    if (!query.trim()) {
      return [];
    }

    const {
      caseSensitive = false,
      includeOCR = true,
      includeTags = true,
      includeDescription = true,
      maxResults = 50,
      minRelevanceScore = 0.1,
      searchFields = ['name', 'content', 'ocrText', 'tags', 'description']
    } = options;

    const searchTerm = caseSensitive ? query : query.toLowerCase();
    const results: SearchResult[] = [];

    for (const [documentId, index] of Array.from(this.searchIndex.entries())) {
      const matches: SearchMatch[] = [];
      let totalScore = 0;

      // Search in document name
      if (searchFields.includes('name')) {
        const nameMatches = this.findMatches(index.name, searchTerm, caseSensitive, 'name');
        matches.push(...nameMatches);
        totalScore += nameMatches.length * 10; // Name matches are weighted higher
      }

      // Search in content
      if (searchFields.includes('content') && index.content) {
        const contentMatches = this.findMatches(index.content, searchTerm, caseSensitive, 'content');
        matches.push(...contentMatches);
        totalScore += contentMatches.length * 5;
      }

      // Search in OCR text
      if (searchFields.includes('ocrText') && includeOCR && index.ocrText) {
        const ocrMatches = this.findMatches(index.ocrText, searchTerm, caseSensitive, 'ocrText');
        matches.push(...ocrMatches);
        totalScore += ocrMatches.length * 3;
      }

      // Search in tags
      if (searchFields.includes('tags') && includeTags && index.tags.length > 0) {
        const tagText = index.tags.join(' ');
        const tagMatches = this.findMatches(tagText, searchTerm, caseSensitive, 'tags');
        matches.push(...tagMatches);
        totalScore += tagMatches.length * 8; // Tag matches are weighted high
      }

      // Search in description
      if (searchFields.includes('description') && includeDescription && index.description) {
        const descMatches = this.findMatches(index.description, searchTerm, caseSensitive, 'description');
        matches.push(...descMatches);
        totalScore += descMatches.length * 4;
      }

      if (matches.length > 0 && totalScore >= minRelevanceScore) {
        const preview = this.generatePreview(index, searchTerm, caseSensitive);
        
        results.push({
          documentId,
          documentName: index.name,
          documentType: index.type,
          matches,
          relevanceScore: totalScore,
          preview
        });
      }
    }

    // Sort by relevance score and limit results
    return results
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, maxResults);
  }

  /**
   * Find matches in text
   */
  private findMatches(
    text: string,
    searchTerm: string,
    caseSensitive: boolean,
    field: SearchMatch['field']
  ): SearchMatch[] {
    const matches: SearchMatch[] = [];
    const searchText = caseSensitive ? text : text.toLowerCase();
    const term = caseSensitive ? searchTerm : searchTerm.toLowerCase();

    let startIndex = 0;
    while (true) {
      const index = searchText.indexOf(term, startIndex);
      if (index === -1) break;

      const endIndex = index + term.length;
      const originalText = text.substring(index, endIndex);
      const highlightedText = this.highlightText(text, index, endIndex);

      matches.push({
        field,
        text: originalText,
        startIndex: index,
        endIndex,
        highlightedText
      });

      startIndex = endIndex;
    }

    return matches;
  }

  /**
   * Highlight matched text
   */
  private highlightText(text: string, startIndex: number, endIndex: number): string {
    const before = text.substring(0, startIndex);
    const match = text.substring(startIndex, endIndex);
    const after = text.substring(endIndex);
    
    return `${before}<mark class="bg-yellow-200 text-yellow-900 px-1 rounded">${match}</mark>${after}`;
  }

  /**
   * Generate search preview
   */
  private generatePreview(
    index: SearchIndex,
    searchTerm: string,
    caseSensitive: boolean
  ): string {
    const term = caseSensitive ? searchTerm : searchTerm.toLowerCase();
    const searchText = caseSensitive ? index.content : index.content.toLowerCase();
    
    const indexOfMatch = searchText.indexOf(term);
    if (indexOfMatch === -1) {
      // No match in content, try OCR text
      const ocrText = caseSensitive ? index.ocrText : index.ocrText?.toLowerCase();
      const ocrIndex = ocrText?.indexOf(term);
      if (ocrIndex !== -1 && ocrIndex !== undefined && ocrText) {
        return this.extractPreview(ocrText, ocrIndex, 150);
      }
      return index.content.substring(0, 150) + '...';
    }

    return this.extractPreview(index.content, indexOfMatch, 150);
  }

  /**
   * Extract preview text around match
   */
  private extractPreview(text: string, matchIndex: number, maxLength: number): string {
    const start = Math.max(0, matchIndex - maxLength / 2);
    const end = Math.min(text.length, matchIndex + maxLength / 2);
    
    let preview = text.substring(start, end);
    
    if (start > 0) {
      preview = '...' + preview;
    }
    if (end < text.length) {
      preview = preview + '...';
    }
    
    return preview;
  }

  /**
   * Get search suggestions based on partial query
   */
  getSuggestions(partialQuery: string, maxSuggestions: number = 10): string[] {
    if (!partialQuery.trim()) {
      return [];
    }

    const suggestions = new Set<string>();
    const query = partialQuery.toLowerCase();

    for (const index of Array.from(this.searchIndex.values())) {
      // Search in tags
      for (const tag of index.tags) {
        if (tag.toLowerCase().includes(query)) {
          suggestions.add(tag);
        }
      }

      // Search in words from content
      const words = index.content.split(/\s+/);
      for (const word of words) {
        const cleanWord = word.replace(/[^\w]/g, '').toLowerCase();
        if (cleanWord.length > 2 && cleanWord.includes(query)) {
          suggestions.add(word);
        }
      }

      if (suggestions.size >= maxSuggestions) break;
    }

    return Array.from(suggestions).slice(0, maxSuggestions);
  }

  /**
   * Get search statistics
   */
  getSearchStats(): {
    totalDocuments: number;
    totalContentLength: number;
    averageContentLength: number;
    documentsWithOCR: number;
  } {
    let totalContentLength = 0;
    let documentsWithOCR = 0;

    for (const index of Array.from(this.searchIndex.values())) {
      totalContentLength += index.content.length;
      if (index.ocrText) {
        documentsWithOCR++;
      }
    }

    return {
      totalDocuments: this.searchIndex.size,
      totalContentLength,
      averageContentLength: this.searchIndex.size > 0 ? totalContentLength / this.searchIndex.size : 0,
      documentsWithOCR
    };
  }

  /**
   * Clear search index
   */
  clearIndex(): void {
    this.searchIndex.clear();
  }
}

// Create singleton instance
const contentSearchService = new ContentSearchService();

export default contentSearchService; 