import React from 'react';
import type { User } from '../App';

export function formatTextWithMentions(text: string, users: User[]): React.ReactNode[] {
  if (!text) return [];
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  const mentionRegex = /@(\w+)/g;
  let match;
  while ((match = mentionRegex.exec(text)) !== null) {
    const matchIndex = match.index;
    const mentionText = match[0];
    if (matchIndex > lastIndex) {
      parts.push(text.slice(lastIndex, matchIndex));
    }
    parts.push(
      <span key={matchIndex} className="text-blue-400 font-medium">
        {mentionText}
      </span>
    );
    lastIndex = matchIndex + mentionText.length;
  }
  if (lastIndex < text.length) {
    parts.push(text.slice(lastIndex));
  }
  return parts;
}

export interface FormattedTextProps {
  text: string;
  users: User[];
  className?: string;
}

export const FormattedText: React.FC<FormattedTextProps> = ({ text, users, className = "" }) => {
  return <span className={className}>{formatTextWithMentions(text, users)}</span>;
}; 