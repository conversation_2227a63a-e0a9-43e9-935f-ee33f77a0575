import { useState, useRef, useCallback } from 'react';
import type { User } from '../App';

export interface MentionSuggestion {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
}

export interface MentionPosition {
  start: number;
  end: number;
  query: string;
}

export interface UseMentionsReturn {
  value: string;
  setValue: (value: string) => void;
  suggestions: MentionSuggestion[];
  showSuggestions: boolean;
  setShowSuggestions: (show: boolean) => void;
  selectedIndex: number;
  mentionPosition: MentionPosition | null;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  handleSuggestionClick: (suggestion: MentionSuggestion) => void;
  handleSuggestionHover: (index: number) => void;
  insertMention: (suggestion: MentionSuggestion) => void;
  getMentionedUsers: () => string[];
  closeDropdown: () => void;
}

export const useMentions = (
  users: User[],
  initialValue: string = '',
  textareaRef?: React.RefObject<HTMLTextAreaElement | null>
): UseMentionsReturn => {
  const [value, setValue] = useState(initialValue);
  const [suggestions, setSuggestions] = useState<MentionSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [mentionPosition, setMentionPosition] = useState<MentionPosition | null>(null);

  // Convert users to mention suggestions
  const userSuggestions: MentionSuggestion[] = users.map(user => ({
    id: user.id,
    name: user.name,
    email: user.email,
    avatar: user.avatar,
    role: user.role
  }));

  // Find mention position in text
  const findMentionPosition = useCallback((text: string, cursorPosition: number): MentionPosition | null => {
    const beforeCursor = text.slice(0, cursorPosition);
    const mentionRegex = /@(\w*)$/;
    const match = beforeCursor.match(mentionRegex);
    
    if (match) {
      const start = beforeCursor.lastIndexOf('@');
      const query = match[1];
      return {
        start,
        end: cursorPosition,
        query
      };
    }
    
    return null;
  }, []);

  // Filter suggestions based on query
  const filterSuggestions = useCallback((query: string): MentionSuggestion[] => {
    if (!query) return userSuggestions;
    const filtered = userSuggestions.filter(user =>
      user.name.toLowerCase().includes(query.toLowerCase()) ||
      user.email.toLowerCase().includes(query.toLowerCase())
    );
    console.log('useMentions filterSuggestions for query:', query, filtered);
    return filtered.slice(0, 8); // Limit to 8 suggestions
  }, [userSuggestions]);

  // Insert mention into text
  const insertMention = useCallback((suggestion: MentionSuggestion) => {
    if (!mentionPosition || !textareaRef?.current) return value;
    const beforeMention = value.slice(0, mentionPosition.start);
    const afterMention = value.slice(mentionPosition.end);
    const mentionText = `@${suggestion.name} `;
    const newValue = beforeMention + mentionText + afterMention;
    setValue(newValue);
    const newCursorPosition = mentionPosition.start + mentionText.length;
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
        textareaRef.current.focus();
      }
    }, 0);
    setShowSuggestions(false);
    setMentionPosition(null);
    setSelectedIndex(0);
    return newValue;
  }, [value, mentionPosition, textareaRef]);

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    
    const cursorPosition = e.target.selectionStart || 0;
    const position = findMentionPosition(newValue, cursorPosition);
    
    if (position) {
      setMentionPosition(position);
      const filtered = filterSuggestions(position.query);
      setSuggestions(filtered);
      setShowSuggestions(filtered.length > 0);
      setSelectedIndex(0);
    } else {
      setShowSuggestions(false);
      setMentionPosition(null);
    }
  }, [findMentionPosition, filterSuggestions]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!showSuggestions) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (suggestions[selectedIndex]) {
          insertMention(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setMentionPosition(null);
        break;
      case 'Tab':
        e.preventDefault();
        if (suggestions[selectedIndex]) {
          insertMention(suggestions[selectedIndex]);
        }
        break;
    }
  }, [showSuggestions, suggestions, selectedIndex, insertMention]);

  // Handle suggestion click
  const handleSuggestionClick = useCallback((suggestion: MentionSuggestion) => {
    insertMention(suggestion);
  }, [insertMention]);

  // Handle suggestion hover
  const handleSuggestionHover = useCallback((index: number) => {
    setSelectedIndex(index);
  }, []);

  // Get mentioned users from text
  const getMentionedUsers = useCallback((): string[] => {
    const mentionRegex = /@(\w+)/g;
    const mentions: string[] = [];
    let match;
    
    while ((match = mentionRegex.exec(value)) !== null) {
      const mentionName = match[1];
      const user = userSuggestions.find(u => 
        u.name.toLowerCase() === mentionName.toLowerCase()
      );
      if (user) {
        mentions.push(user.id);
      }
    }
    
    return mentions;
  }, [value, userSuggestions]);

  const closeDropdown = useCallback(() => {
    setShowSuggestions(false);
    setMentionPosition(null);
  }, []);

  return {
    value,
    setValue,
    suggestions,
    showSuggestions,
    setShowSuggestions,
    selectedIndex,
    mentionPosition,
    handleInputChange,
    handleKeyDown,
    handleSuggestionClick,
    handleSuggestionHover,
    insertMention,
    getMentionedUsers,
    closeDropdown
  };
}; 